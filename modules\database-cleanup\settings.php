<?php
/**
 * Database Cleanup Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$database_cleanup = new Redco_Database_Cleanup();
$is_enabled = redco_is_module_enabled('database-cleanup');

// Get current settings
$auto_cleanup = redco_get_module_option('database-cleanup', 'auto_cleanup', false);
$cleanup_interval = redco_get_module_option('database-cleanup', 'cleanup_interval', 'weekly');
$cleanup_revisions = redco_get_module_option('database-cleanup', 'cleanup_revisions', true);
$cleanup_auto_drafts = redco_get_module_option('database-cleanup', 'cleanup_auto_drafts', true);
$cleanup_trashed_posts = redco_get_module_option('database-cleanup', 'cleanup_trashed_posts', true);
$cleanup_spam_comments = redco_get_module_option('database-cleanup', 'cleanup_spam_comments', true);
$cleanup_trashed_comments = redco_get_module_option('database-cleanup', 'cleanup_trashed_comments', true);
$cleanup_expired_transients = redco_get_module_option('database-cleanup', 'cleanup_expired_transients', true);
$cleanup_orphaned_postmeta = redco_get_module_option('database-cleanup', 'cleanup_orphaned_postmeta', true);
$cleanup_orphaned_commentmeta = redco_get_module_option('database-cleanup', 'cleanup_orphaned_commentmeta', true);
$keep_revisions = redco_get_module_option('database-cleanup', 'keep_revisions', 5);

// Get current database statistics
$cleanup_stats = redco_get_cleanup_stats();
$stats = array(
    'total_cleaned' => array_sum($cleanup_stats),
    'last_cleanup' => get_option('redco_last_cleanup', 'Never'),
    'auto_cleanup' => $auto_cleanup ? 'Enabled' : 'Disabled',
    'cleanup_interval' => ucfirst($cleanup_interval)
);
?>

<div class="redco-module-tab" data-module="database-cleanup">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-database"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Database Cleanup', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Clean and optimize your WordPress database for better performance and reduced storage', 'redco-optimizer'); ?></p>
                </div>
            </div>

        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="database-cleanup">
                    <!-- Automatic Cleanup Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-clock"></span>
                                <?php _e('Automatic Cleanup', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Enable Automatic Cleanup', 'redco-optimizer'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="settings[auto_cleanup]" value="1" <?php checked($auto_cleanup); ?>>
                                            <?php _e('Run database cleanup automatically', 'redco-optimizer'); ?>
                                        </label>
                                        <p class="description">
                                            <?php _e('Automatically clean up database on a scheduled basis.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="cleanup_interval"><?php _e('Cleanup Interval', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[cleanup_interval]" id="cleanup_interval">
                                            <option value="daily" <?php selected($cleanup_interval, 'daily'); ?>><?php _e('Daily', 'redco-optimizer'); ?></option>
                                            <option value="weekly" <?php selected($cleanup_interval, 'weekly'); ?>><?php _e('Weekly', 'redco-optimizer'); ?></option>
                                            <option value="monthly" <?php selected($cleanup_interval, 'monthly'); ?>><?php _e('Monthly', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('How often to run automatic cleanup.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Cleanup Options Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Cleanup Options', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Select which types of data to clean from your database.', 'redco-optimizer'); ?>
                            </p>

                            <div class="cleanup-options" style="display: grid; gap: 15px; margin-top: 20px;">
            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_revisions]"
                           value="1"
                           <?php checked($cleanup_revisions); ?>>
                    <?php _e('Post Revisions', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('database-cleanup', 'cleanup-options', 'post_revisions', 'Remove old post and page revisions'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['revisions']); ?>)</span>
                </label>
                <div class="sub-option">
                    <label for="keep_revisions"><?php _e('Keep latest:', 'redco-optimizer'); ?></label>
                    <input type="number"
                           name="settings[keep_revisions]"
                           id="keep_revisions"
                           value="<?php echo esc_attr($keep_revisions); ?>"
                           min="0"
                           max="50"
                           style="width: 80px;">
                    <span><?php _e('revisions per post', 'redco-optimizer'); ?></span>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_auto_drafts]"
                           value="1"
                           <?php checked($cleanup_auto_drafts); ?>>
                    <?php _e('Auto Drafts', 'redco-optimizer'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['auto_drafts']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove auto-draft posts older than 7 days.', 'redco-optimizer'); ?>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_trashed_posts]"
                           value="1"
                           <?php checked($cleanup_trashed_posts); ?>>
                    <?php _e('Trashed Posts', 'redco-optimizer'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['trashed_posts']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove posts in trash older than 30 days.', 'redco-optimizer'); ?>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_spam_comments]"
                           value="1"
                           <?php checked($cleanup_spam_comments); ?>>
                    <?php _e('Spam Comments', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('database-cleanup', 'cleanup-options', 'spam_comments', 'Remove comments marked as spam'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['spam_comments']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove all spam comments.', 'redco-optimizer'); ?>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_trashed_comments]"
                           value="1"
                           <?php checked($cleanup_trashed_comments); ?>>
                    <?php _e('Trashed Comments', 'redco-optimizer'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['trashed_comments']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove all trashed comments.', 'redco-optimizer'); ?>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_expired_transients]"
                           value="1"
                           <?php checked($cleanup_expired_transients); ?>>
                    <?php _e('Expired Transients', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('database-cleanup', 'cleanup-options', 'expired_transients', 'Remove expired transient data that WordPress and plugins use for temporary caching'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['expired_transients']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove expired transient options.', 'redco-optimizer'); ?>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_orphaned_postmeta]"
                           value="1"
                           <?php checked($cleanup_orphaned_postmeta); ?>>
                    <?php _e('Orphaned Post Meta', 'redco-optimizer'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['orphaned_postmeta']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove post meta for non-existent posts.', 'redco-optimizer'); ?>
                </div>
            </div>

            <div class="cleanup-option">
                <label class="checkbox-item">
                    <input type="checkbox"
                           name="settings[cleanup_orphaned_commentmeta]"
                           value="1"
                           <?php checked($cleanup_orphaned_commentmeta); ?>>
                    <?php _e('Orphaned Comment Meta', 'redco-optimizer'); ?>
                    <span class="count">(<?php echo number_format($cleanup_stats['orphaned_commentmeta']); ?>)</span>
                </label>
                <div class="description">
                    <?php _e('Remove comment meta for non-existent comments.', 'redco-optimizer'); ?>
                </div>
            </div>
                            </div>
                        </div>
                    </div>

                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Database Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Database Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-revisions">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['revisions']); ?></span>
                                <span class="stat-label"><?php _e('Revisions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-drafts">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['auto_drafts']); ?></span>
                                <span class="stat-label"><?php _e('Auto Drafts', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-spam">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['spam_comments']); ?></span>
                                <span class="stat-label"><?php _e('Spam Comments', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-transients">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['expired_transients']); ?></span>
                                <span class="stat-label"><?php _e('Transients', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Cleanup -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Manual Cleanup', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="run-cleanup-now" class="button button-secondary" data-redco-action="optimize_database" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-database"></span>
                            <?php _e('Run Cleanup Now', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Run database cleanup immediately with current settings.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-database"></span>
            <h3><?php _e('Database Cleanup Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access database cleanup and optimization features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>


