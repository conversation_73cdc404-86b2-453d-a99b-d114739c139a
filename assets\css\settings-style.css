/**
 * Redco Optimizer Settings Page Styles
 * Modern, clean, and professional styling for the settings interface
 */

/* Settings Page Header */
.redco-optimizer-settings .redco-settings-header {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    margin: 0 0 30px 0;
    padding: 30px 0;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.redco-settings-header .settings-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
    margin: 0;
    padding: 0 40px;
}

.redco-settings-header h1 {
    color: white;
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-settings-header h1 .dashicons {
    font-size: 32px;
}

.redco-settings-header .settings-subtitle {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

.redco-settings-header .settings-actions .button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.redco-settings-header .settings-actions .button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Settings Container */
.redco-settings-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Custom Navigation Tabs */
.redco-nav-tab-wrapper {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    border: none;
    background: none;
    flex-wrap: wrap;
}

.redco-nav-tab {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s ease;
    min-width: 180px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.redco-nav-tab:hover {
    border-color: #4CAF50;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.redco-nav-tab.redco-nav-tab-active {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    border-color: #388E3C;
    color: white;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.redco-nav-tab .dashicons {
    font-size: 20px;
    flex-shrink: 0;
}

.redco-nav-tab .tab-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.redco-nav-tab .tab-title {
    font-weight: 600;
    font-size: 14px;
}

.redco-nav-tab .tab-description {
    font-size: 12px;
    opacity: 0.8;
}

/* Settings Content */
.redco-settings-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.settings-content-wrapper {
    padding: 40px;
}

/* Settings Sections */
.redco-settings-section {
    margin-bottom: 40px;
}

.redco-settings-section:last-child {
    margin-bottom: 0;
}

.settings-section-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.settings-section-header h2 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.settings-section-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* Settings Cards Grid */
.settings-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

/* Settings Cards */
.settings-card {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.settings-card:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.1);
}

.settings-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-card-header .dashicons {
    font-size: 24px;
    color: #4CAF50;
}

.settings-card-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.settings-card-content {
    padding: 24px;
}

/* Setting Items */
.setting-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.setting-control {
    flex-shrink: 0;
}

.setting-info {
    flex: 1;
}

.setting-info h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.setting-info p {
    margin: 0 0 12px 0;
    color: #666;
    line-height: 1.5;
}

.setting-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ff9800;
    font-size: 14px;
    font-weight: 500;
}

.setting-warning .dashicons {
    font-size: 16px;
}



/* Form Footer */
.settings-form-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #f0f0f0;
    text-align: right;
}

.redco-save-button {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%) !important;
    border: none !important;
    color: white !important;
    padding: 12px 30px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3) !important;
    transition: all 0.3s ease !important;
}

.redco-save-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
}

/* Setup Wizard Card */
.setup-wizard-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.setup-wizard-card:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.1);
}

.wizard-card-content {
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
}

.wizard-info {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
}

.wizard-info .dashicons {
    font-size: 48px;
    color: #4CAF50;
    flex-shrink: 0;
}

.wizard-text h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.wizard-text p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

.wizard-actions .button {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%) !important;
    border: none !important;
    color: white !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3) !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.wizard-actions .button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
}

/* Custom Select Styling */
.redco-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.redco-select:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Custom Text Input Styling */
.redco-text-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.redco-text-input:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.redco-text-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Setting Benefits */
.setting-benefits {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.benefit-item {
    background: rgba(76, 175, 80, 0.1);
    color: #388E3C;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Setting Recommendation */
.setting-recommendation {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
}

.setting-recommendation .dashicons {
    font-size: 16px;
}

/* Setting Note */
.setting-note {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
    margin-top: 8px;
}

.setting-note .dashicons {
    font-size: 16px;
    color: #2196F3;
}

/* Loading States */
.redco-settings-form.loading {
    opacity: 0.6;
    pointer-events: none;
}

.redco-settings-form.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    margin: -16px 0 0 -16px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Messages */
.redco-settings-success {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
}

.redco-settings-success .dashicons {
    font-size: 20px;
}

/* Error Messages */
.redco-settings-error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 16px rgba(244, 67, 54, 0.3);
}

.redco-settings-error .dashicons {
    font-size: 20px;
}

/* Advanced Settings Specific Styles */
.settings-card.advanced-warning {
    border-color: #ff9800;
}

.settings-card.advanced-warning .settings-card-header {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.settings-card.advanced-warning .settings-card-header .dashicons {
    color: #ff9800;
}

/* Role Selection Grid */
.role-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 12px;
}

.role-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.role-checkbox-item:hover {
    background: #e8f5e8;
    border-color: #4CAF50;
}

.role-checkbox-item input[type="checkbox"] {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-settings-header .settings-header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .redco-nav-tab-wrapper {
        flex-direction: column;
    }

    .redco-nav-tab {
        min-width: auto;
    }

    .settings-cards-grid {
        grid-template-columns: 1fr;
    }

    .wizard-card-content {
        flex-direction: column;
        text-align: center;
    }

    .settings-content-wrapper {
        padding: 20px;
    }

    .setting-item {
        flex-direction: column;
        gap: 12px;
    }

    .setting-control {
        align-self: flex-start;
    }

    .role-selection-grid {
        grid-template-columns: 1fr;
    }

    .settings-form-footer {
        text-align: center;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .redco-settings-content {
        background: #1e1e1e;
        color: #e0e0e0;
    }

    .settings-card {
        background: #2d2d2d;
        border-color: #404040;
    }

    .settings-card-header {
        background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
        color: #e0e0e0;
    }

    .setting-info h4 {
        color: #e0e0e0;
    }

    .setting-info p {
        color: #b0b0b0;
    }

    .redco-select {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }

    .role-checkbox-item {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }
}
