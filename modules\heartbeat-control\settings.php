<?php
/**
 * Heartbeat Control Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$heartbeat_control = new Redco_Heartbeat_Control();
$is_enabled = redco_is_module_enabled('heartbeat-control');

// Get current settings
$admin_heartbeat = redco_get_module_option('heartbeat-control', 'admin_heartbeat', 'modify');
$admin_frequency = redco_get_module_option('heartbeat-control', 'admin_frequency', 60);
$editor_heartbeat = redco_get_module_option('heartbeat-control', 'editor_heartbeat', 'modify');
$editor_frequency = redco_get_module_option('heartbeat-control', 'editor_frequency', 30);
$frontend_heartbeat = redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable');
$frontend_frequency = redco_get_module_option('heartbeat-control', 'frontend_frequency', 60);

// Get heartbeat statistics - only if module is enabled
$stats = array(
    'admin_status' => $admin_heartbeat,
    'admin_frequency' => $admin_frequency,
    'editor_status' => $editor_heartbeat,
    'editor_frequency' => $editor_frequency,
    'frontend_status' => $frontend_heartbeat,
    'frontend_frequency' => $frontend_frequency
);
if ($is_enabled && class_exists('Redco_Heartbeat_Control')) {
    $stats = $heartbeat_control->get_stats();
}
?>

<div class="redco-module-tab" data-module="heartbeat-control">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-heart"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Heartbeat Control', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Control WordPress Heartbeat API frequency to reduce server load and improve performance', 'redco-optimizer'); ?></p>
                </div>
            </div>

        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="heartbeat-control">
                    <!-- Admin Area Heartbeat Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-generic"></span>
                                <?php _e('Admin Area Heartbeat', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="admin_heartbeat"><?php _e('Admin Heartbeat Control', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[admin_heartbeat]" id="admin_heartbeat">
                                            <option value="default" <?php selected($admin_heartbeat, 'default'); ?>><?php _e('Default (15 seconds)', 'redco-optimizer'); ?></option>
                                            <option value="modify" <?php selected($admin_heartbeat, 'modify'); ?>><?php _e('Modify Frequency', 'redco-optimizer'); ?></option>
                                            <option value="disable" <?php selected($admin_heartbeat, 'disable'); ?>><?php _e('Disable Completely', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('Control heartbeat in WordPress admin area (dashboard, settings, etc.).', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr class="frequency-row" data-parent="admin_heartbeat" data-show-when="modify">
                                    <th scope="row">
                                        <label for="admin_frequency"><?php _e('Admin Frequency', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[admin_frequency]" id="admin_frequency">
                                            <option value="15" <?php selected($admin_frequency, 15); ?>><?php _e('15 seconds (Default)', 'redco-optimizer'); ?></option>
                                            <option value="30" <?php selected($admin_frequency, 30); ?>><?php _e('30 seconds', 'redco-optimizer'); ?></option>
                                            <option value="60" <?php selected($admin_frequency, 60); ?>><?php _e('60 seconds', 'redco-optimizer'); ?></option>
                                            <option value="120" <?php selected($admin_frequency, 120); ?>><?php _e('2 minutes', 'redco-optimizer'); ?></option>
                                            <option value="300" <?php selected($admin_frequency, 300); ?>><?php _e('5 minutes', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('How often heartbeat should run in admin area.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Post Editor Heartbeat Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-edit"></span>
                                <?php _e('Post Editor Heartbeat', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="editor_heartbeat"><?php _e('Editor Heartbeat Control', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[editor_heartbeat]" id="editor_heartbeat">
                                            <option value="default" <?php selected($editor_heartbeat, 'default'); ?>><?php _e('Default (15 seconds)', 'redco-optimizer'); ?></option>
                                            <option value="modify" <?php selected($editor_heartbeat, 'modify'); ?>><?php _e('Modify Frequency', 'redco-optimizer'); ?></option>
                                            <option value="disable" <?php selected($editor_heartbeat, 'disable'); ?>><?php _e('Disable Completely', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('Control heartbeat in post/page editor (for autosave and user activity).', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr class="frequency-row" data-parent="editor_heartbeat" data-show-when="modify">
                                    <th scope="row">
                                        <label for="editor_frequency"><?php _e('Editor Frequency', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[editor_frequency]" id="editor_frequency">
                                            <option value="15" <?php selected($editor_frequency, 15); ?>><?php _e('15 seconds (Default)', 'redco-optimizer'); ?></option>
                                            <option value="30" <?php selected($editor_frequency, 30); ?>><?php _e('30 seconds', 'redco-optimizer'); ?></option>
                                            <option value="60" <?php selected($editor_frequency, 60); ?>><?php _e('60 seconds', 'redco-optimizer'); ?></option>
                                            <option value="120" <?php selected($editor_frequency, 120); ?>><?php _e('2 minutes', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('How often heartbeat should run in post editor. Lower values provide better autosave protection.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Frontend Heartbeat Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-site"></span>
                                <?php _e('Frontend Heartbeat', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="frontend_heartbeat"><?php _e('Frontend Heartbeat Control', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[frontend_heartbeat]" id="frontend_heartbeat">
                                            <option value="default" <?php selected($frontend_heartbeat, 'default'); ?>><?php _e('Default (15 seconds)', 'redco-optimizer'); ?></option>
                                            <option value="modify" <?php selected($frontend_heartbeat, 'modify'); ?>><?php _e('Modify Frequency', 'redco-optimizer'); ?></option>
                                            <option value="disable" <?php selected($frontend_heartbeat, 'disable'); ?>><?php _e('Disable Completely (Recommended)', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('Control heartbeat on frontend pages. Usually not needed for visitors.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr class="frequency-row" data-parent="frontend_heartbeat" data-show-when="modify">
                                    <th scope="row">
                                        <label for="frontend_frequency"><?php _e('Frontend Frequency', 'redco-optimizer'); ?></label>
                                    </th>
                                    <td>
                                        <select name="settings[frontend_frequency]" id="frontend_frequency">
                                            <option value="30" <?php selected($frontend_frequency, 30); ?>><?php _e('30 seconds', 'redco-optimizer'); ?></option>
                                            <option value="60" <?php selected($frontend_frequency, 60); ?>><?php _e('60 seconds', 'redco-optimizer'); ?></option>
                                            <option value="120" <?php selected($frontend_frequency, 120); ?>><?php _e('2 minutes', 'redco-optimizer'); ?></option>
                                            <option value="300" <?php selected($frontend_frequency, 300); ?>><?php _e('5 minutes', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('How often heartbeat should run on frontend pages.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Current Settings', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-admin">
                                <span class="stat-value status-<?php echo esc_attr($stats['admin_status']); ?>">
                                    <?php echo esc_html(ucfirst($stats['admin_status'])); ?>
                                    <?php if ($stats['admin_status'] === 'modify'): ?>
                                        (<?php echo $stats['admin_frequency']; ?>s)
                                    <?php endif; ?>
                                </span>
                                <span class="stat-label"><?php _e('Admin Status', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-editor">
                                <span class="stat-value status-<?php echo esc_attr($stats['editor_status']); ?>">
                                    <?php echo esc_html(ucfirst($stats['editor_status'])); ?>
                                    <?php if ($stats['editor_status'] === 'modify'): ?>
                                        (<?php echo $stats['editor_frequency']; ?>s)
                                    <?php endif; ?>
                                </span>
                                <span class="stat-label"><?php _e('Editor Status', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-frontend">
                                <span class="stat-value status-<?php echo esc_attr($stats['frontend_status']); ?>">
                                    <?php echo esc_html(ucfirst($stats['frontend_status'])); ?>
                                    <?php if ($stats['frontend_status'] === 'modify'): ?>
                                        (<?php echo $stats['frontend_frequency']; ?>s)
                                    <?php endif; ?>
                                </span>
                                <span class="stat-label"><?php _e('Frontend Status', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Heartbeat API is used for:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px; font-size: 12px;">
                            <li><?php _e('Autosaving posts and pages while editing', 'redco-optimizer'); ?></li>
                            <li><?php _e('Showing user activity and post locks', 'redco-optimizer'); ?></li>
                            <li><?php _e('Displaying notifications and updates', 'redco-optimizer'); ?></li>
                            <li><?php _e('Real-time features in admin area', 'redco-optimizer'); ?></li>
                        </ul>
                        <p><strong><?php _e('Recommendations:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px; font-size: 12px;">
                            <li><?php _e('Disable on frontend (visitors don\'t need it)', 'redco-optimizer'); ?></li>
                            <li><?php _e('Reduce frequency in admin area (60 seconds is usually fine)', 'redco-optimizer'); ?></li>
                            <li><?php _e('Keep default or 30 seconds in post editor for better autosave', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-heart"></span>
            <h3><?php _e('Heartbeat Control Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to control WordPress Heartbeat API frequency.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Show/hide frequency options based on heartbeat control selection
    function toggleFrequencyOptions() {
        $('.frequency-row').each(function() {
            const $row = $(this);
            const parent = $row.data('parent');
            const showWhen = $row.data('show-when');
            const parentValue = $('#' + parent).val();

            if (parentValue === showWhen) {
                $row.show();
            } else {
                $row.hide();
            }
        });
    }

    // Initial toggle
    toggleFrequencyOptions();

    // Toggle on change
    $('select[id$="_heartbeat"]').on('change', toggleFrequencyOptions);
});
</script>

<style>
.status-default { color: #666; }
.status-modify { color: #4CAF50; }
.status-disable { color: #d63638; }
.frequency-row { margin-left: 20px; border-left: 3px solid #4CAF50; padding-left: 15px; }
</style>
