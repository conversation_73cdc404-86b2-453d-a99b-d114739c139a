<?php
/**
 * Page Cache Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$page_cache = new Redco_Page_Cache();
$is_enabled = redco_is_module_enabled('page-cache');

// Get current settings - Default to 6 hours for optimal PageSpeed
$cache_expiration = redco_get_module_option('page-cache', 'expiration', 21600);
$excluded_pages = redco_get_module_option('page-cache', 'excluded_pages', array());

// Get all pages for exclusion list
$pages = redco_get_pages();

// Get cache statistics - only if module is enabled
$cache_stats = array(
    'cached_pages' => 0,
    'cache_size' => '0 B',
    'cache_hits' => 0,
    'cache_misses' => 0,
    'hit_ratio' => 0
);
if ($is_enabled && class_exists('Redco_Page_Cache')) {
    $cache_stats = $page_cache->get_cache_stats();
}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="page-cache">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-performance"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Page Cache', 'redco-optimizer'); ?></h1>
                    <p><?php _e('High-performance page caching for faster loading times and better user experience', 'redco-optimizer'); ?></p>
                </div>
            </div>

        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="page-cache">
                    <!-- Cache Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('Cache Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">

                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cache_expiration">
                                            <?php _e('Cache Expiration Time', 'redco-optimizer'); ?>
                                        </label>
                                    </th>
                                    <td>
                                        <select name="settings[expiration]" id="cache_expiration">
                                            <option value="1800" <?php selected($cache_expiration, 1800); ?>><?php _e('30 minutes', 'redco-optimizer'); ?></option>
                                            <option value="3600" <?php selected($cache_expiration, 3600); ?>><?php _e('1 hour', 'redco-optimizer'); ?></option>
                                            <option value="7200" <?php selected($cache_expiration, 7200); ?>><?php _e('2 hours', 'redco-optimizer'); ?></option>
                                            <option value="21600" <?php selected($cache_expiration, 21600); ?>><?php _e('6 hours', 'redco-optimizer'); ?></option>
                                            <option value="43200" <?php selected($cache_expiration, 43200); ?>><?php _e('12 hours', 'redco-optimizer'); ?></option>
                                            <option value="86400" <?php selected($cache_expiration, 86400); ?>><?php _e('24 hours', 'redco-optimizer'); ?></option>
                                        </select>
                                        <p class="description">
                                            <?php _e('How long to keep cached pages before regenerating them.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($pages)): ?>
                    <!-- Excluded Pages Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php _e('Excluded Pages', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Select pages that should not be cached (e.g., contact forms, user-specific content).', 'redco-optimizer'); ?>
                            </p>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Pages to Exclude', 'redco-optimizer'); ?></th>
                                    <td>
                                        <div class="checkbox-list">
                                            <?php foreach ($pages as $page_id => $page_title): ?>
                                                <div class="checkbox-item" style="margin-bottom: 8px;">
                                                    <label>
                                                        <input type="checkbox"
                                                               name="settings[excluded_pages][]"
                                                               value="<?php echo esc_attr($page_id); ?>"
                                                               <?php checked(in_array($page_id, $excluded_pages)); ?>>
                                                        <?php echo esc_html($page_title); ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>

                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Cache Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Cache Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-value"><?php echo number_format($cache_stats['cache_hits']); ?></span>
                                <span class="stat-label"><?php _e('Cache Hits', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo number_format($cache_stats['cache_misses']); ?></span>
                                <span class="stat-label"><?php _e('Cache Misses', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo $cache_stats['cache_size']; ?></span>
                                <span class="stat-label"><?php _e('Cache Size', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo round($cache_stats['hit_ratio'], 1); ?>%</span>
                                <span class="stat-label"><?php _e('Hit Ratio', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Cache Actions', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="clear-page-cache" class="button button-secondary" data-redco-action="clear_page_cache" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-trash"></span>
                            <?php _e('Clear Page Cache', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Clear all cached pages. New cache will be generated when pages are visited.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-performance"></span>
            <h3><?php _e('Page Cache Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access high-performance page caching features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>


