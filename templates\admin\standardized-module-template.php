<?php
/**
 * Standardized Module Template
 * Use this template for all new modules to ensure consistent layout
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Module configuration - CUSTOMIZE THESE VALUES
$module_key = 'MODULE_KEY'; // e.g., 'database-cleanup'
$module_class = 'MODULE_CLASS'; // e.g., 'Redco_Database_Cleanup'
$module_icon = 'MODULE_ICON'; // e.g., 'dashicons-database'
$module_title = 'MODULE_TITLE'; // e.g., 'Database Cleanup'
$module_description = 'MODULE_DESCRIPTION'; // e.g., 'Clean and optimize your WordPress database'

// Get module instance
$module_instance = new $module_class();
$is_enabled = redco_is_module_enabled($module_key);
$stats = $module_instance->get_stats();

// Get current settings - CUSTOMIZE BASED ON MODULE
$current_settings = array(
    // Add module-specific settings here
    // 'setting_name' => redco_get_module_option($module_key, 'setting_name', 'default_value'),
);
?>

<div class="redco-module-tab" data-module="<?php echo esc_attr($module_key); ?>">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons <?php echo esc_attr($module_icon); ?>"></span>
                </div>
                <div class="header-text">
                    <h1><?php echo esc_html($module_title); ?></h1>
                    <p><?php echo esc_html($module_description); ?></p>
                </div>
            </div>
            <div class="header-actions">
                <div class="module-toggle-wrapper">
                    <label for="module-toggle-<?php echo esc_attr($module_key); ?>">
                        <?php _e('Enable Module', 'redco-optimizer'); ?>
                    </label>
                    <input type="checkbox" id="module-toggle-<?php echo esc_attr($module_key); ?>" class="module-toggle" 
                           data-module="<?php echo esc_attr($module_key); ?>" <?php checked($is_enabled); ?> />
                </div>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="<?php echo esc_attr($module_key); ?>">
                    
                    <!-- Settings Section 1 -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('Settings Section Title', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Description of this settings section.', 'redco-optimizer'); ?>
                            </p>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="setting_name">
                                            <?php _e('Setting Label', 'redco-optimizer'); ?>
                                        </label>
                                    </th>
                                    <td>
                                        <!-- Add form controls here -->
                                        <input type="checkbox" name="settings[setting_name]" value="1" id="setting_name" />
                                        <p class="description">
                                            <?php _e('Setting description.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Settings Section 2 (if needed) -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Advanced Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <table class="form-table">
                                <!-- Add more settings here -->
                            </table>
                        </div>
                    </div>

                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-value"><?php echo esc_html($stats['stat1'] ?? '0'); ?></span>
                                <span class="stat-label"><?php _e('Stat 1', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo esc_html($stats['stat2'] ?? '0'); ?></span>
                                <span class="stat-label"><?php _e('Stat 2', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo esc_html($stats['stat3'] ?? '0'); ?></span>
                                <span class="stat-label"><?php _e('Stat 3', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo esc_html($stats['stat4'] ?? '0'); ?></span>
                                <span class="stat-label"><?php _e('Stat 4', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Module Actions', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="module-action-button" class="button button-secondary" data-redco-action="module_action" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Action Button', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Description of what this action does.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Estimated Improvements:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><?php _e('Improvement 1', 'redco-optimizer'); ?></li>
                            <li><?php _e('Improvement 2', 'redco-optimizer'); ?></li>
                            <li><?php _e('Improvement 3', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons <?php echo esc_attr($module_icon); ?>"></span>
            <h3><?php echo sprintf(__('%s Module Disabled', 'redco-optimizer'), $module_title); ?></h3>
            <p><?php echo sprintf(__('Enable this module to access %s features.', 'redco-optimizer'), strtolower($module_title)); ?></p>
            <button type="button" class="button button-primary enable-module" data-module="<?php echo esc_attr($module_key); ?>">
                <?php echo sprintf(__('Enable %s', 'redco-optimizer'), $module_title); ?>
            </button>
        </div>
    </div>
    <?php endif; ?>
</div>
