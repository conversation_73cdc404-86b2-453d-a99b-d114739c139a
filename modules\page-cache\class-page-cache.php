<?php
/**
 * Page Cache Module for Redco Optimizer
 *
 * Implements full page caching functionality using WordPress object cache
 * and output buffering for improved performance.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Page_Cache {

    /**
     * Cache group
     */
    private $cache_group = 'redco_page_cache';

    /**
     * Cache expiration time (in seconds)
     */
    private $cache_expiration = 21600; // 6 hours default for optimal PageSpeed

    /**
     * Excluded pages
     */
    private $excluded_pages = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('page-cache')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $settings = redco_get_module_option('page-cache', 'settings', array());

        $this->cache_expiration = isset($settings['expiration']) ? (int) $settings['expiration'] : 3600;
        $this->excluded_pages = isset($settings['excluded_pages']) ? (array) $settings['excluded_pages'] : array();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Only cache for non-admin, non-logged-in users
        if (!is_admin() && !is_user_logged_in()) {
            add_action('init', array($this, 'start_cache'), 1);
            add_action('wp_footer', array($this, 'end_cache'), 999);
        }

        // Clear cache hooks
        add_action('save_post', array($this, 'clear_post_cache'));
        add_action('comment_post', array($this, 'clear_post_cache_by_comment'));
        add_action('wp_update_comment_count', array($this, 'clear_post_cache'));

        // AJAX handlers
        add_action('wp_ajax_redco_clear_page_cache', array($this, 'ajax_clear_cache'));

        // Admin hooks
        if (is_admin()) {
            add_action('admin_bar_menu', array($this, 'add_admin_bar_menu'), 999);
        }
    }

    /**
     * Start output buffering for caching
     */
    public function start_cache() {
        // Skip caching for certain conditions
        if ($this->should_skip_cache()) {
            return;
        }

        $cache_key = $this->get_cache_key();
        $cached_content = wp_cache_get($cache_key, $this->cache_group);

        if ($cached_content !== false) {
            // Record cache hit
            $this->record_cache_hit();

            // Serve cached content
            echo $cached_content;
            echo '<!-- Served from Redco Page Cache -->';
            exit;
        }

        // Record cache miss
        $this->record_cache_miss();

        // Start output buffering
        ob_start(array($this, 'cache_output'));
    }

    /**
     * End caching and store output
     */
    public function end_cache() {
        // This is handled by the ob_start callback
    }

    /**
     * Cache output callback
     */
    public function cache_output($content) {
        // Don't cache if content is too small (likely an error page)
        if (strlen($content) < 255) {
            return $content;
        }

        // Don't cache if there are PHP errors
        if (strpos($content, '<b>Fatal error</b>') !== false ||
            strpos($content, '<b>Warning</b>') !== false) {
            return $content;
        }

        $cache_key = $this->get_cache_key();

        // Store in cache
        wp_cache_set($cache_key, $content, $this->cache_group, $this->cache_expiration);

        // Update cache statistics
        $this->update_cache_stats($content);

        // Add cache comment
        $content .= '<!-- Cached by Redco Page Cache on ' . date('Y-m-d H:i:s') . ' -->';

        return $content;
    }

    /**
     * Check if caching should be skipped
     */
    private function should_skip_cache() {
        global $wp_query;

        // Skip for admin, AJAX, cron
        if (is_admin() || wp_doing_ajax() || wp_doing_cron()) {
            return true;
        }

        // Skip for logged-in users
        if (is_user_logged_in()) {
            return true;
        }

        // Skip for POST requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return true;
        }

        // Skip if there are query parameters (except common ones)
        $allowed_params = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content');
        $query_params = array_keys($_GET);
        $filtered_params = array_diff($query_params, $allowed_params);

        if (!empty($filtered_params)) {
            return true;
        }

        // Skip for 404 pages
        if (is_404()) {
            return true;
        }

        // Skip for search results
        if (is_search()) {
            return true;
        }

        // Skip for feeds
        if (is_feed()) {
            return true;
        }

        // Skip for excluded pages
        if (is_page() && in_array(get_the_ID(), $this->excluded_pages)) {
            return true;
        }

        // Skip for WooCommerce pages if WooCommerce is active
        if (redco_is_woocommerce_active()) {
            if (is_cart() || is_checkout() || is_account_page()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate cache key for current request
     */
    private function get_cache_key() {
        $url = $_SERVER['REQUEST_URI'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Include mobile detection in cache key
        $is_mobile = wp_is_mobile() ? 'mobile' : 'desktop';

        return md5($url . $is_mobile . $user_agent);
    }

    /**
     * Clear cache for specific post
     */
    public function clear_post_cache($post_id) {
        if (wp_is_post_revision($post_id)) {
            return;
        }

        // Clear homepage cache
        $this->clear_homepage_cache();

        // Clear post/page cache
        $post_url = get_permalink($post_id);
        if ($post_url) {
            $cache_key = md5(parse_url($post_url, PHP_URL_PATH) . 'desktop');
            wp_cache_delete($cache_key, $this->cache_group);

            $cache_key_mobile = md5(parse_url($post_url, PHP_URL_PATH) . 'mobile');
            wp_cache_delete($cache_key_mobile, $this->cache_group);
        }

        // Clear category/tag archives if it's a post
        if (get_post_type($post_id) === 'post') {
            $this->clear_taxonomy_caches($post_id);
        }
    }

    /**
     * Clear cache when comment is posted
     */
    public function clear_post_cache_by_comment($comment_id) {
        $comment = get_comment($comment_id);
        if ($comment) {
            $this->clear_post_cache($comment->comment_post_ID);
        }
    }

    /**
     * Clear homepage cache
     */
    private function clear_homepage_cache() {
        $home_url = parse_url(home_url(), PHP_URL_PATH);
        $cache_key = md5($home_url . 'desktop');
        wp_cache_delete($cache_key, $this->cache_group);

        $cache_key_mobile = md5($home_url . 'mobile');
        wp_cache_delete($cache_key_mobile, $this->cache_group);
    }

    /**
     * Clear taxonomy caches
     */
    private function clear_taxonomy_caches($post_id) {
        $taxonomies = get_object_taxonomies(get_post_type($post_id));

        foreach ($taxonomies as $taxonomy) {
            $terms = get_the_terms($post_id, $taxonomy);
            if ($terms && !is_wp_error($terms)) {
                foreach ($terms as $term) {
                    $term_url = get_term_link($term);
                    if (!is_wp_error($term_url)) {
                        $cache_key = md5(parse_url($term_url, PHP_URL_PATH) . 'desktop');
                        wp_cache_delete($cache_key, $this->cache_group);

                        $cache_key_mobile = md5(parse_url($term_url, PHP_URL_PATH) . 'mobile');
                        wp_cache_delete($cache_key_mobile, $this->cache_group);
                    }
                }
            }
        }
    }

    /**
     * Clear all cache
     */
    public function clear_all_cache() {
        // Clear WordPress object cache
        wp_cache_flush();

        // Clear file-based cache
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $success = true;

        if (is_dir($cache_dir)) {
            $success = redco_clear_directory_recursive($cache_dir, false); // Don't remove the main directory
        }

        // Reset cache statistics
        update_option('redco_cache_hits', 0);
        update_option('redco_cache_misses', 0);

        return $success;
    }

    /**
     * Get cache statistics - Real data
     */
    public function get_cache_stats() {
        $cache_hits = get_option('redco_cache_hits', 0);
        $cache_misses = get_option('redco_cache_misses', 0);
        $total_requests = $cache_hits + $cache_misses;

        // Calculate hit ratio
        $hit_ratio = $total_requests > 0 ? round(($cache_hits / $total_requests) * 100, 1) : 0;

        // Count cached pages
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $cached_pages = 0;

        if (is_dir($cache_dir)) {
            $cached_pages = $this->count_cached_pages($cache_dir);
        }

        return array(
            'cache_hits' => $cache_hits,
            'cache_misses' => $cache_misses,
            'cache_size' => $this->get_cache_size(),
            'cached_pages' => $cached_pages,
            'hit_ratio' => $hit_ratio
        );
    }

    /**
     * Count cached pages
     */
    private function count_cached_pages($directory) {
        $count = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'html') {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get cache size - Real measurements
     */
    private function get_cache_size() {
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $total_size = 0;

        if (is_dir($cache_dir)) {
            $total_size = $this->calculate_directory_size($cache_dir);
        }

        return redco_format_bytes($total_size);
    }

    /**
     * Calculate directory size recursively
     */
    private function calculate_directory_size($directory) {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }

    /**
     * Record cache hit
     */
    public function record_cache_hit() {
        $hits = get_option('redco_cache_hits', 0);
        update_option('redco_cache_hits', $hits + 1);
    }

    /**
     * Record cache miss
     */
    public function record_cache_miss() {
        $misses = get_option('redco_cache_misses', 0);
        update_option('redco_cache_misses', $misses + 1);
    }

    /**
     * Update cache statistics when content is cached
     */
    private function update_cache_stats($content) {
        $stats = get_option('redco_page_cache_stats', array(
            'pages_cached' => 0,
            'total_size' => 0,
            'last_cached' => 0,
            'average_size' => 0
        ));

        $stats['pages_cached']++;
        $stats['total_size'] += strlen($content);
        $stats['last_cached'] = time();
        $stats['average_size'] = $stats['pages_cached'] > 0 ? round($stats['total_size'] / $stats['pages_cached']) : 0;

        update_option('redco_page_cache_stats', $stats);
    }

    /**
     * Get comprehensive cache statistics
     */
    public function get_stats() {
        $cache_hits = get_option('redco_cache_hits', 0);
        $cache_misses = get_option('redco_cache_misses', 0);
        $cache_stats = get_option('redco_page_cache_stats', array(
            'pages_cached' => 0,
            'total_size' => 0,
            'last_cached' => 0,
            'average_size' => 0
        ));

        $total_requests = $cache_hits + $cache_misses;
        $hit_ratio = $total_requests > 0 ? round(($cache_hits / $total_requests) * 100, 1) : 0;

        // Get real cache directory size
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        $cache_size_bytes = 0;
        $cached_files = 0;

        if (is_dir($cache_dir)) {
            $cache_size_bytes = $this->calculate_directory_size($cache_dir);
            $cached_files = $this->count_cached_pages($cache_dir);
        }

        return array(
            'hits' => $cache_hits,
            'misses' => $cache_misses,
            'hit_ratio' => $hit_ratio,
            'pages_cached' => max($cache_stats['pages_cached'], $cached_files),
            'cache_size' => redco_format_bytes($cache_size_bytes),
            'cache_size_bytes' => $cache_size_bytes,
            'average_page_size' => $cache_stats['average_size'] > 0 ? redco_format_bytes($cache_stats['average_size']) : '0 B',
            'last_cached' => $cache_stats['last_cached'],
            'enabled' => redco_is_module_enabled('page-cache'),
            'expiration' => $this->cache_expiration,
            'performance_impact' => $this->calculate_performance_impact($hit_ratio, $cache_hits)
        );
    }

    /**
     * Calculate performance impact
     */
    private function calculate_performance_impact($hit_ratio, $cache_hits) {
        if ($cache_hits === 0) {
            return array(
                'status' => 'no_data',
                'message' => 'No cache hits recorded yet',
                'estimated_time_saved' => 0
            );
        }

        // Estimate time saved (average page generation time ~500ms, cache serve ~50ms)
        $time_saved_per_hit = 450; // milliseconds
        $total_time_saved = $cache_hits * $time_saved_per_hit;

        $status = 'good';
        if ($hit_ratio < 30) {
            $status = 'poor';
        } elseif ($hit_ratio < 60) {
            $status = 'fair';
        }

        return array(
            'status' => $status,
            'message' => "Cache is working well with {$hit_ratio}% hit ratio",
            'estimated_time_saved' => $total_time_saved,
            'time_saved_formatted' => $this->format_time_saved($total_time_saved)
        );
    }

    /**
     * Format time saved for display
     */
    private function format_time_saved($milliseconds) {
        if ($milliseconds < 1000) {
            return $milliseconds . 'ms';
        } elseif ($milliseconds < 60000) {
            return round($milliseconds / 1000, 1) . 's';
        } elseif ($milliseconds < 3600000) {
            return round($milliseconds / 60000, 1) . 'm';
        } else {
            return round($milliseconds / 3600000, 1) . 'h';
        }
    }

    /**
     * Start cache preloading
     */
    public function start_preload() {
        // Get URLs to preload
        $urls = $this->get_preload_urls();

        if (empty($urls)) {
            return false;
        }

        // Preload in background (simplified version)
        foreach (array_slice($urls, 0, 10) as $url) { // Limit to 10 URLs for performance
            $this->preload_url($url);
        }

        return true;
    }

    /**
     * Get URLs to preload
     */
    private function get_preload_urls() {
        $urls = array();

        // Add homepage
        $urls[] = home_url('/');

        // Add recent posts
        $recent_posts = get_posts(array(
            'numberposts' => 5,
            'post_status' => 'publish'
        ));

        foreach ($recent_posts as $post) {
            $urls[] = get_permalink($post->ID);
        }

        // Add pages
        $pages = get_pages(array(
            'number' => 5,
            'post_status' => 'publish'
        ));

        foreach ($pages as $page) {
            $urls[] = get_permalink($page->ID);
        }

        return array_unique($urls);
    }

    /**
     * Preload a specific URL
     */
    private function preload_url($url) {
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => 'Redco Optimizer Cache Preloader'
        ));

        return !is_wp_error($response);
    }

    /**
     * AJAX handler for clearing cache
     */
    public function ajax_clear_cache() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        $result = $this->clear_all_cache();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Page cache cleared successfully', 'redco-optimizer')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear page cache', 'redco-optimizer')
            ));
        }
    }

    /**
     * Add admin bar menu
     */
    public function add_admin_bar_menu($wp_admin_bar) {
        if (!current_user_can('manage_options')) {
            return;
        }

        $wp_admin_bar->add_menu(array(
            'id' => 'redco-clear-cache',
            'title' => __('Clear Page Cache', 'redco-optimizer'),
            'href' => wp_nonce_url(admin_url('admin-ajax.php?action=redco_clear_page_cache'), 'redco_optimizer_nonce', 'nonce'),
            'meta' => array(
                'title' => __('Clear Redco Page Cache', 'redco-optimizer')
            )
        ));
    }
}

// Initialize the module
new Redco_Page_Cache();
