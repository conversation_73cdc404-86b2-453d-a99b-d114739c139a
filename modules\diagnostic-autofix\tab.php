<?php
/**
 * Diagnostic & Auto-Fix Module Tab - Professional Interface
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Verification functionality removed - no longer needed

// Get module instance and real-time data
$diagnostic_module = new Redco_Diagnostic_AutoFix();
$stats = $diagnostic_module->get_stats();
$last_scan = get_option('redco_diagnostic_results', array());
$is_enabled = redco_is_module_enabled('diagnostic-autofix');

// Get cached scan results (don't force fresh scan on page load)
// Fresh scans will be triggered by user actions via AJAX

// Get real-time performance data
$site_health = array(
    'overall_score' => $stats['health_score'] ?? 85,
    'performance_score' => $stats['performance_score'] ?? 78,
    'issues_found' => $stats['issues_found'] ?? 0,
    'critical_issues' => $stats['critical_issues'] ?? 0,
    'auto_fixable' => $stats['auto_fixable_issues'] ?? 0,
    'fixes_applied' => $stats['fixes_applied'] ?? 0,
    'trend' => $stats['health_trend'] ?? 0,
    'last_check' => $stats['last_scan_time'] ?? 0
);

// Debug: Ensure we have a valid score
if (empty($site_health['overall_score']) || !is_numeric($site_health['overall_score'])) {
    $site_health['overall_score'] = 85; // Fallback score
}

$performance_metrics = array(
    'score' => $stats['performance_score'] ?? 78,
    'load_time' => $stats['avg_load_time'] ?? '2.3s',
    'core_vitals' => $stats['core_vitals_score'] ?? 82
);

// Use cached stats for display (no fresh scan on page load)
// Fresh scans will be triggered by user actions via AJAX
?>

<div class="redco-module-tab" data-module="diagnostic-autofix">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Website Diagnostic & Auto-Fix', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-analytics"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Website Diagnostic & Auto-Fix', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Professional-grade performance analysis and intelligent optimization', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($site_health['auto_fixable'] > 0): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Auto-Fix Available', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($site_health['overall_score'] >= 90): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    <?php _e('Excellent Health', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="run-new-scan" data-scan-type="comprehensive">
                                <span class="dashicons dashicons-search"></span>
                                <?php _e('Run New Scan', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="apply-auto-fixes" <?php echo $site_health['auto_fixable'] == 0 ? 'disabled' : ''; ?>>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Auto-Fix', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="export-diagnostic-report">
                                <span class="dashicons dashicons-download"></span>
                                <?php _e('Export Report', 'redco-optimizer'); ?>
                            </button>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($site_health['overall_score']); ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Health Score', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($site_health['performance_score']); ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Performance', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($site_health['issues_found']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Issues', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($site_health['critical_issues']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Critical', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($site_health['auto_fixable']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Auto-Fix', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($site_health['fixes_applied']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Fixed', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<!-- Toast Notification Container -->
<div class="redco-toast-container" id="redco-toast-container"></div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content diagnostic-layout">
        <div class="redco-content-main">
            <!-- Diagnostic Overview -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-chart-line"></span>
                        <?php _e('Diagnostic Overview', 'redco-optimizer'); ?>
                    </h3>
                    <div class="card-actions">
                        <button type="button" class="button button-primary action-button" id="run-comprehensive-scan" data-scan-type="comprehensive">
                            <span class="dashicons dashicons-search"></span>
                            <?php _e('Run Comprehensive Scan', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="diagnostic-overview-grid">
                        <div class="overview-stat">
                            <div class="stat-value <?php echo $stats['health_score'] >= 80 ? 'good' : ($stats['health_score'] >= 60 ? 'warning' : 'critical'); ?>">
                                <?php echo $stats['health_score']; ?>%
                            </div>
                            <div class="stat-label"><?php _e('Health Score', 'redco-optimizer'); ?></div>
                        </div>

                        <div class="overview-stat">
                            <div class="stat-value <?php echo $stats['performance_score'] >= 80 ? 'good' : ($stats['performance_score'] >= 60 ? 'warning' : 'critical'); ?>">
                                <?php echo $stats['performance_score']; ?>%
                            </div>
                            <div class="stat-label"><?php _e('Performance Score', 'redco-optimizer'); ?></div>
                        </div>

                        <div class="overview-stat">
                            <div class="stat-value <?php echo $stats['issues_found'] == 0 ? 'good' : ($stats['issues_found'] <= 5 ? 'warning' : 'critical'); ?>">
                                <?php echo $stats['issues_found']; ?>
                            </div>
                            <div class="stat-label"><?php _e('Issues Found', 'redco-optimizer'); ?></div>
                        </div>

                        <div class="overview-stat">
                            <div class="stat-value <?php echo $stats['critical_issues'] == 0 ? 'good' : 'critical'; ?>">
                                <?php echo $stats['critical_issues']; ?>
                            </div>
                            <div class="stat-label"><?php _e('Critical Issues', 'redco-optimizer'); ?></div>
                        </div>

                        <div class="overview-stat">
                            <div class="stat-value <?php echo $stats['auto_fixable_issues'] > 0 ? 'warning' : 'good'; ?>">
                                <?php echo $stats['auto_fixable_issues']; ?>
                            </div>
                            <div class="stat-label"><?php _e('Auto-Fixable', 'redco-optimizer'); ?></div>
                        </div>

                        <div class="overview-stat">
                            <div class="stat-value">
                                <?php echo $stats['fixes_applied']; ?>
                            </div>
                            <div class="stat-label"><?php _e('Fixes Applied', 'redco-optimizer'); ?></div>
                        </div>
                    </div>

                    <?php if ($stats['last_scan_time'] > 0): ?>
                    <div class="last-scan-info">
                        <p>
                            <strong><?php _e('Last Scan:', 'redco-optimizer'); ?></strong>
                            <?php echo human_time_diff($stats['last_scan_time']) . ' ' . __('ago', 'redco-optimizer'); ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>



            <!-- Recent Issues -->
            <?php
            // Check if there are issues to display
            $has_issues = !empty($last_scan['issues']);
            $issues_to_display = $last_scan['issues'] ?? array();
            ?>
            <?php if ($has_issues): ?>
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('Recent Issues Found', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="issues-list" id="diagnostic-issues-list">
                        <?php
                        // CRITICAL FIX: Use the correct issues array
                        $displayed_issues = array_slice($issues_to_display, 0, 10); // Show first 10 issues
                        foreach ($displayed_issues as $index => $issue):
                        ?>
                        <div class="issue-item <?php echo esc_attr($issue['severity']); ?>" data-issue-id="<?php echo esc_attr($issue['id'] ?? $index); ?>">
                            <?php if ($issue['severity'] === 'critical'): ?>
                                <span class="dashicons dashicons-dismiss"></span>
                            <?php elseif ($issue['severity'] === 'high'): ?>
                                <span class="dashicons dashicons-warning"></span>
                            <?php elseif ($issue['severity'] === 'medium'): ?>
                                <span class="dashicons dashicons-info"></span>
                            <?php else: ?>
                                <span class="dashicons dashicons-lightbulb"></span>
                            <?php endif; ?>

                            <div class="issue-content">
                                <div class="issue-title"><?php echo esc_html($issue['title']); ?></div>
                                <div class="issue-description"><?php echo esc_html($issue['description']); ?></div>
                                <div class="issue-meta">
                                    <span class="issue-category"><?php echo esc_html(ucfirst($issue['category'])); ?></span>
                                    <span class="issue-severity"><?php echo esc_html(ucfirst($issue['severity'])); ?></span>
                                    <?php if ($issue['auto_fixable']): ?>
                                    <span class="status-indicator success">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php _e('Auto-Fixable', 'redco-optimizer'); ?>
                                    </span>
                                    <?php else: ?>
                                    <span class="status-indicator warning">
                                        <span class="dashicons dashicons-admin-tools"></span>
                                        <?php _e('Manual Fix Required', 'redco-optimizer'); ?>
                                    </span>
                                    <?php endif; ?>
                                </div>

                                <?php if (!$issue['auto_fixable']): ?>
                                <!-- How to Solve Tips for Non-Auto-Fixable Issues -->
                                <div class="how-to-solve-tip" id="how-to-solve-<?php echo esc_attr($issue['id'] ?? $index); ?>" style="display: none;">
                                    <?php echo $diagnostic_module->get_how_to_solve_tip($issue); ?>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Right-aligned action buttons -->
                            <div class="issue-actions-right">
                                <?php if ($issue['auto_fixable']): ?>
                                <button type="button" class="action-button primary fix-single-issue" data-issue-id="<?php echo esc_attr($issue['id']); ?>">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Fix Now', 'redco-optimizer'); ?>
                                </button>
                                <?php else: ?>
                                <button type="button" class="toggle-how-to-solve" data-issue-id="<?php echo esc_attr($issue['id'] ?? $index); ?>">
                                    <span class="dashicons dashicons-arrow-down"></span>
                                    <?php _e('How to solve', 'redco-optimizer'); ?>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <?php if (count($issues_to_display) > 10): ?>
                        <div class="show-more-issues">
                            <button type="button" class="button button-link" id="show-all-issues">
                                <?php echo sprintf(__('Show %d more issues...', 'redco-optimizer'), count($issues_to_display) - 10); ?>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php elseif (!$has_issues && (!empty($last_scan) && isset($last_scan['timestamp']))): ?>
            <!-- No Issues Found Message -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-yes-alt"></span>
                        <?php _e('Scan Results', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="no-issues-found">
                        <div class="success-message">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <h4><?php _e('Great! No Issues Found', 'redco-optimizer'); ?></h4>
                            <p><?php _e('Your website appears to be well-optimized. The last scan completed successfully and found no issues that need attention.', 'redco-optimizer'); ?></p>
                            <p class="scan-info">
                                <strong><?php _e('Last Scan:', 'redco-optimizer'); ?></strong>
                                <?php
                                $scan_timestamp = $last_scan['timestamp'] ?? time();
                                echo human_time_diff($scan_timestamp) . ' ' . __('ago', 'redco-optimizer');
                                ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Fix History -->
            <div class="redco-card recent-fixes-section">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-backup"></span>
                        <?php _e('Recent Fixes', 'redco-optimizer'); ?>
                    </h3>
                    <div class="card-actions">
                        <button type="button" class="refresh-recent-fixes" id="refresh-fix-history">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Refresh', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="fix-history-list" id="fix-history-list">
                        <!-- Fix history will be loaded via AJAX -->
                        <div class="recent-fixes-loading">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Loading fix history...', 'redco-optimizer'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="redco-content-sidebar">
            <!-- Diagnostic Statistics -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-chart-bar"></span>
                        <?php _e('Diagnostic Statistics', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="sidebar-stats">
                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('Last Scan', 'redco-optimizer'); ?></div>
                            <div class="stat-value">
                                <?php echo $stats['last_scan_time'] > 0 ? human_time_diff($stats['last_scan_time']) . ' ' . __('ago', 'redco-optimizer') : __('Never', 'redco-optimizer'); ?>
                            </div>
                        </div>

                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('Scan Frequency', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo ucfirst($stats['scan_frequency']); ?></div>
                        </div>

                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('Auto-Fix Status', 'redco-optimizer'); ?></div>
                            <div class="stat-value">
                                <?php echo $stats['auto_fix_enabled'] ? __('Enabled', 'redco-optimizer') : __('Disabled', 'redco-optimizer'); ?>
                            </div>
                        </div>

                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('Emergency Mode', 'redco-optimizer'); ?></div>
                            <div class="stat-value">
                                <?php echo $stats['emergency_mode_active'] ? __('Active', 'redco-optimizer') : __('Inactive', 'redco-optimizer'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-admin-generic"></span>
                        <?php _e('Quick Actions', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="sidebar-actions">
                        <button type="button" class="button button-secondary button-block" id="apply-auto-fixes" <?php echo $stats['auto_fixable_issues'] == 0 ? 'disabled' : ''; ?>>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Apply Auto-Fixes', 'redco-optimizer'); ?>
                            <?php if ($stats['auto_fixable_issues'] > 0): ?>
                            <span class="button-badge"><?php echo $stats['auto_fixable_issues']; ?></span>
                            <?php endif; ?>
                        </button>

                        <button type="button" class="button button-secondary button-block" id="export-diagnostic-report">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export Report', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary button-block" id="emergency-recovery-btn" style="display: none; background: #dc3545; color: white;">
                            <span class="dashicons dashicons-sos"></span>
                            <?php _e('Emergency Recovery', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Emergency Controls -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('Emergency Controls', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="emergency-controls">
                        <?php if ($stats['emergency_mode_active']): ?>
                        <button type="button" class="button button-secondary button-block" id="deactivate-emergency-mode">
                            <span class="dashicons dashicons-dismiss"></span>
                            <?php _e('Deactivate Emergency Mode', 'redco-optimizer'); ?>
                        </button>
                        <?php else: ?>
                        <button type="button" class="button button-secondary button-block" id="activate-emergency-mode">
                            <span class="dashicons dashicons-warning"></span>
                            <?php _e('Activate Emergency Mode', 'redco-optimizer'); ?>
                        </button>
                        <?php endif; ?>

                        <p class="description">
                            <?php _e('Emergency mode temporarily disables heavy optimization modules to improve site stability.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-admin-tools"></span>
            <h3><?php _e('Diagnostic & Auto-Fix Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access comprehensive WordPress performance diagnostics and intelligent auto-fix capabilities.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
/* Professional Diagnostic Interface */
.diagnostic-professional {
    background: #f8f9fa;
    min-height: 100vh;
}

/* Header Section - MAXIMUM COMPACT */
.module-header-section {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    color: white;
    padding: 20px 40px 20px 50px;
    margin: 0 -20px 30px -20px;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    min-height: 163px;
    border-radius: 0;
}

.header-content {
    max-width: 1200px;
    max-height: 0px;
    margin: 0 auto;
    padding: 0px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-icon {
    width: 38px;
    height: 38px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-icon .dashicons {
    font-size: 18px;
    color: white;
}

.module-title {
    font-size: 1.6em;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

.module-subtitle {
    font-size: 0.9em;
    margin: 1px 0 0 0;
    opacity: 0.9;
    font-weight: 300;
    line-height: 1.2;
}

.header-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    flex-shrink: 0;
}

.health-score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    gap: 2px;
}

/* Health Score Circle - Clean Ring Design with High Contrast */
.health-score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    border: 6px solid rgba(255, 255, 255, 0.3);
}

.health-score-circle::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 6px solid transparent;
    border-top-color: var(--health-score-color, #2196F3);
    transform: rotate(calc(var(--score-percentage, 0) * 3.6deg));
    transition: transform 1.2s ease-out;
    z-index: 0;
}

.health-score-circle:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.health-score-circle.animated::before {
    animation: healthScoreReveal 1.2s ease-out;
}

@keyframes healthScoreReveal {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(calc(var(--score-percentage) * 3.6deg));
    }
}

/* Health Score Colors Based on Ranges */
.health-score-circle[data-score] {
    --score-percentage: attr(data-score);
}

/* Default colors - JavaScript will override these dynamically */
.health-score-circle {
    --health-score-color: #3182CE;
    --score-percentage: 0;
}

/* Health Score Number Styling */
.health-score-circle .score-number {
    position: relative;
    z-index: 2;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
    text-align: center;
}

/* Ensure score number is always visible */
#header-score-value {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #2c3e50 !important;
    text-align: center !important;
    z-index: 2 !important;
    position: relative !important;
}

.score-trend {
    position: absolute;
    top: -6px;
    right: -6px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    border-radius: 8px;
    padding: 2px 6px;
    font-size: 0.6em;
    font-weight: 600;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.trend-up {
    color: #2e7d32;
}

.trend-down {
    color: #c62828;
}

.trend-neutral {
    color: #5a6c7d;
}

.score-details {
    margin-top: 4px;
    text-align: center;
}

.score-label {
    font-size: 10px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
    opacity: 1;
    text-shadow: none;
}

.last-updated {
    font-size: 8px;
    opacity: 0.8;
    font-weight: 400;
    line-height: 1.2;
    color: #5a6c7d;
    text-shadow: none;
}

/* Score update animation */
.score-circle.score-updated {
    animation: scoreUpdate 2s ease-in-out;
}

@keyframes scoreUpdate {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    25% {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(76, 175, 80, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 30px rgba(76, 175, 80, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .header-actions {
        margin-left: 0;
        justify-content: center;
    }

    .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: #e0e0e0;
    }

    .score-value {
        font-size: 1.6em;
    }
}



/* Professional Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    line-height: 1.4;
}

.btn-primary {
    background: white;
    color: #4CAF50;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
    font-size: 16px;
}

/* Dashboard Layout */
.diagnostic-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.dashboard-main {
    display: grid;
    gap: 30px;
}

/* Performance Overview Cards */
.performance-overview {
    margin-bottom: 30px;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.health-card .card-icon {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
}

.performance-card .card-icon {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
}

.issues-card .card-icon {
    background: linear-gradient(135deg, #fff3e0, #ffcc02);
    color: #ef6c00;
}

.optimization-card .card-icon {
    background: linear-gradient(135deg, #f3e5f5, #ce93d8);
    color: #7b1fa2;
}

.card-header h3 {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50;
}

.metric-value {
    font-size: 2.5em;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
}

.metric-value.good { color: #4CAF50; }
.metric-value.warning { color: #FF9800; }
.metric-value.critical { color: #F44336; }

.metric-label {
    font-size: 0.9em;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 12px;
}

.metric-trend {
    font-size: 0.85em;
    font-weight: 500;
}

.trend-up { color: #4CAF50; }
.trend-down { color: #F44336; }
.trend-stable { color: #6c757d; }

.metric-detail {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 8px;
}

.metric-breakdown {
    display: flex;
    gap: 15px;
    font-size: 0.85em;
    margin-top: 8px;
}

.metric-breakdown .critical {
    color: #F44336;
    font-weight: 500;
}

.metric-breakdown .fixable {
    color: #4CAF50;
    font-weight: 500;
}

/* Legacy Layout Support - MAXIMUM COMPACT */
.diagnostic-layout {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    min-height: 100vh;
    position: relative;
}

.diagnostic-layout.processing {
    pointer-events: none;
}

.diagnostic-layout.processing .redco-content-main,
.diagnostic-layout.processing .redco-content-sidebar {
    transition: none;
    position: relative;
}

.redco-content-main {
    flex: 1;
    min-width: 0;
    width: calc(100% - 272px); /* Reduced sidebar width to 260px + gap */
    transition: width 0.3s ease;
}

.diagnostic-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 8px;
    margin-bottom: 12px;
}

.overview-stat {
    text-align: center;
    padding: 12px 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    color: #2c3e50;
}

.overview-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

.stat-value {
    font-size: 1.6em;
    font-weight: 700;
    margin-bottom: 3px;
    line-height: 1.2;
    color: #2c3e50; /* Default dark color for visibility */
}

.stat-value.good { color: #4CAF50 !important; }
.stat-value.warning { color: #FF9800 !important; }
.stat-value.critical { color: #F44336 !important; }

.stat-label {
    font-size: 0.75em;
    color: #2c3e50 !important; /* Dark color for visibility */
    font-weight: 500;
    line-height: 1.2;
}



.action-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px 16px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.action-button:hover {
    border-color: #4CAF50;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.1);
    transform: translateY(-1px);
}

.action-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    transform: none;
}

.action-button .dashicons {
    font-size: 20px;
    color: #4CAF50;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    border-radius: 4px;
}

.button-content {
    flex: 1;
}

.button-title {
    font-weight: 600;
    font-size: 0.95em;
    margin-bottom: 2px;
    color: #2c3e50 !important;
    line-height: 1.2;
}

.button-description {
    font-size: 0.8em;
    color: #6c757d !important;
    line-height: 1.2;
}

/* Fix all text visibility issues */
.redco-card .card-content,
.redco-card .card-content p,
.redco-card .card-content div,
.redco-card .card-content span {
    color: #2c3e50 !important;
}

.last-scan-info p,
.last-scan-info strong {
    color: #2c3e50 !important;
}

.description {
    color: #6c757d !important;
}

/* Toast Notification System */
.redco-toast-container {
    position: absolute;
    width: 100%;
    z-index: 999999;
    margin: 20px 0;
    padding: 0;
    min-height: 0;
    overflow: visible;
}

/* When positioned in body (fallback) */
body > .redco-toast-container {
    position: flex;
    top: 32px;
    right: 20px;
    max-width: 400px;
    width: auto;
    margin: 0;
}

.redco-toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #4CAF50;
    padding: 16px 20px;
    margin: 10px auto;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    transform: scale(0.8) translateY(-20px);
    transition: all 0.3s ease;
    opacity: 0;
    max-width: 800px;
    width: calc(100% - 40px);
}

/* When in body container (fallback) */
body > .redco-toast-container .redco-toast {
    transform: translateX(100%);
    margin: 0 0 10px 0;
    width: 100%;
    max-width: 400px;
}

.redco-toast.show {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Show animation for body container */
body > .redco-toast-container .redco-toast.show {
    transform: translateX(0);
}

.redco-toast.info {
    border-left-color: #2196F3;
}

.redco-toast.warning {
    border-left-color: #FF9800;
}

.redco-toast.error {
    border-left-color: #F44336;
}

.redco-toast.success {
    border-left-color: #4CAF50;
}

.redco-toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.redco-toast-icon .dashicons {
    font-size: 20px;
    color: #4CAF50;
}

.redco-toast.info .redco-toast-icon .dashicons {
    color: #2196F3;
}

.redco-toast.warning .redco-toast-icon .dashicons {
    color: #FF9800;
}

.redco-toast.error .redco-toast-icon .dashicons {
    color: #F44336;
}

.redco-toast-content {
    flex: 1;
    color: #2c3e50;
}

.redco-toast-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #2c3e50;
}

.redco-toast-message {
    font-size: 0.9em;
    line-height: 1.4;
    color: #6c757d;
}

.redco-toast-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    margin-left: 8px;
    flex-shrink: 0;
}

.redco-toast-close:hover {
    color: #2c3e50;
}

/* Enhanced Loading States */
.action-button.loading {
    position: relative;
    pointer-events: none;
    border-color: #4CAF50;
}

.action-button.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 3px solid #e8f5e8;
    border-top: 3px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 2;
}

.action-button.loading .dashicons,
.action-button.loading .button-content {
    opacity: 0.4;
}

.btn.loading {
    position: relative;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Health Score update animation */
.health-score-circle.score-updated {
    animation: healthScoreUpdate 1.5s ease-in-out;
}

@keyframes healthScoreUpdate {
    0% { transform: scale(1); }
    30% { transform: scale(1.08); }
    60% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

/* Health Score Display Container */
.health-score-display {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.15);
    padding: 12px 16px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.health-score-display:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
}

.health-score-display:hover .health-score-circle {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments for health score */
@media (max-width: 768px) {
    .health-score-circle {
        width: 70px;
        height: 70px;
        border-width: 5px;
    }

    .health-score-circle::before {
        top: -5px;
        left: -5px;
        width: 70px;
        height: 70px;
        border-width: 5px;
    }

    .health-score-circle .score-number {
        font-size: 20px;
    }

    .score-label {
        font-size: 11px;
    }

    .last-updated {
        font-size: 9px;
    }
}

.button.loading {
    position: relative;
    pointer-events: none;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #ccc;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Professional Issues List */
.issues-list {
    max-height: 500px;
    overflow-y: auto;
    padding: 4px;
}

.issue-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.issue-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.issue-item.severity-critical {
    border-left: 4px solid #F44336;
    background: linear-gradient(90deg, #fdf2f2 0%, white 20%);
}

.issue-item.severity-high {
    border-left: 4px solid #FF9800;
    background: linear-gradient(90deg, #fff8e1 0%, white 20%);
}

.issue-item.severity-medium {
    border-left: 4px solid #2196F3;
    background: linear-gradient(90deg, #e3f2fd 0%, white 20%);
}

.issue-item.severity-low {
    border-left: 4px solid #4CAF50;
    background: linear-gradient(90deg, #e8f5e8 0%, white 20%);
}

.issue-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.issue-item.severity-critical .issue-icon {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #c62828;
}

.issue-item.severity-high .issue-icon {
    background: linear-gradient(135deg, #fff3e0, #ffcc02);
    color: #ef6c00;
}

.issue-item.severity-medium .issue-icon {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
}

.issue-item.severity-low .issue-icon {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
}

.issue-icon .dashicons {
    font-size: 20px;
}

.issue-content {
    flex: 1;
    min-width: 0;
}

.issue-title {
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 8px;
    color: #2c3e50;
    line-height: 1.3;
}

.issue-description {
    color: #6c757d;
    margin-bottom: 12px;
    line-height: 1.5;
}

.issue-meta {
    display: flex;
    gap: 8px;
    font-size: 0.85em;
    flex-wrap: wrap;
}

.issue-meta span {
    padding: 4px 10px;
    border-radius: 20px;
    background: #f8f9fa;
    color: #6c757d;
    font-weight: 500;
    border: 1px solid #e9ecef;
}

.issue-fixable {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    color: #2e7d32 !important;
    border-color: #4CAF50 !important;
}

.issue-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.fix-single-issue {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.fix-single-issue:hover {
    background: #45a049;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.fix-single-issue:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.fix-single-issue .dashicons {
    font-size: 14px;
}

/* Enhanced Issue Status States */
.issue-item.resolved {
    opacity: 0.7;
    background: linear-gradient(90deg, #e8f5e8 0%, white 20%);
    border-left-color: #4CAF50;
    transform: none;
}

.issue-item.failed {
    background: linear-gradient(90deg, #fdf2f2 0%, white 20%);
    border-left-color: #F44336;
}

.issue-item.processing {
    background: linear-gradient(90deg, #fff8e1 0%, white 20%);
    border-left-color: #FF9800;
    position: relative;
}

.issue-item.processing::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 20px;
    height: 20px;
    border: 2px solid #fff3e0;
    border-top: 2px solid #FF9800;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.issue-status-message {
    margin-top: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85em;
    font-weight: 500;
}

.issue-status-message.success {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border: 1px solid #4CAF50;
}

.issue-status-message.error {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #c62828;
    border: 1px solid #F44336;
}

.issue-status-message.processing {
    background: linear-gradient(135deg, #fff3e0, #ffcc02);
    color: #ef6c00;
    border: 1px solid #FF9800;
}

/* Professional Cards */
.redco-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.redco-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.redco-card .card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f1f3f4;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.redco-card .card-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.redco-card .card-header .dashicons {
    color: #4CAF50;
    font-size: 20px;
}

.redco-card .card-content {
    padding: 24px;
}

.card-actions {
    display: flex;
    gap: 10px;
}

/* Professional Sidebar */
.redco-content-sidebar {
    width: 260px;
    margin-left: 24px;
    flex-shrink: 0;
    position: relative;
}

.sidebar-stats {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.sidebar-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.sidebar-stat:last-child {
    border-bottom: none;
}

.sidebar-stat .stat-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.8em;
}

.sidebar-stat .stat-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.88em !important;
}

.sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.button-block {
    width: 100%;
    justify-content: flex-start;
    text-align: left;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.button-block:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-block .dashicons {
    margin-right: 8px;
    font-size: 16px;
}

.button-badge {
    background: #4CAF50;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 8px;
    display: inline-block;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
}

.emergency-controls .description {
    margin-top: 12px;
    font-size: 0.85em;
    color: #6c757d;
    line-height: 1.5;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #4CAF50;
}

/* Additional Professional Elements */
.last-scan-info {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #4CAF50;
}

.last-scan-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9em;
}

.loading-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.loading-placeholder .dashicons.spin {
    animation: spin 1s linear infinite;
    font-size: 24px;
    color: #4CAF50;
    margin-bottom: 10px;
}

.show-more-issues {
    text-align: center;
    padding: 20px;
}

.show-more-issues .button-link {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.show-more-issues .button-link:hover {
    background: #e8f5e8;
    color: #2e7d32;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .header-actions {
        flex-direction: column;
        gap: 20px;
    }

    .quick-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .diagnostic-layout {
        flex-direction: column;
    }

    .redco-content-sidebar {
        width: 100%;
        margin-left: 0;
        margin-top: 20px;
    }

    .overview-cards {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .diagnostic-header-section {
        margin: -20px -10px 20px -10px;
        padding: 20px 0;
    }

    .header-content {
        padding: 0 10px;
    }

    .module-title {
        font-size: 1.8em;
    }

    .diagnostic-dashboard {
        padding: 0 10px;
    }
}

@media (max-width: 480px) {
    .metric-card {
        padding: 16px;
    }

    .action-button {
        padding: 16px;
        gap: 12px;
    }

    .issue-item {
        padding: 16px;
        gap: 12px;
    }

    .redco-card .card-header {
        padding: 16px 20px;
    }

    .redco-card .card-content {
        padding: 16px;
    }
}

/* Professional Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1; }
}

@keyframes spinRing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loadingDots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

.metric-card {
    animation: fadeIn 0.6s ease-out;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }

.issue-item {
    animation: slideIn 0.4s ease-out;
}

.redco-card {
    animation: fadeIn 0.5s ease-out;
}

/* Module Disabled State */
.redco-module-disabled {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.disabled-message {
    max-width: 500px;
    margin: 0 auto;
}

.disabled-message .dashicons {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 20px;
}

.disabled-message h3 {
    font-size: 1.5em;
    color: #2c3e50;
    margin-bottom: 16px;
}

.disabled-message p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 24px;
}

.enable-module {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.enable-module:hover {
    background: #45a049;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Sidebar Styles */
.redco-content-sidebar {
    width: 260px;
    margin-left: 20px;
}

.sidebar-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.sidebar-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-stat:last-child {
    border-bottom: none;
}

.sidebar-stat .stat-label {
    font-weight: 500;
    color: #666;
    font-size: 0.8em;
}

.sidebar-stat .stat-value {
    font-weight: bold;
    color: #333;
    font-size: 0.88em !important;
}

/* More specific rule for diagnostic statistics */
.redco-card .sidebar-stats .sidebar-stat .stat-value {
    font-size: 0.88em !important;
}

.sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.button-block {
    width: 100%;
    justify-content: flex-start;
    text-align: left;
}

.button-block .dashicons {
    margin-right: 8px;
}

.emergency-controls .description {
    margin-top: 10px;
    font-size: 0.9em;
    color: #666;
    line-height: 1.4;
}

/* Notification Styles */
.redco-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background: #fff;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 20px;
    border-radius: 4px;
    z-index: 999999;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    max-width: 500px;
}

.redco-notification.error {
    border-left-color: #F44336;
}

.redco-notification .dashicons {
    color: #4CAF50;
    font-size: 18px;
}

.redco-notification.error .dashicons {
    color: #F44336;
}

.redco-notification .message {
    flex: 1;
    font-weight: 500;
}

.redco-notification .close-notification {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-notification .close-notification:hover {
    color: #333;
}

/* Progress Modal Styles */
.redco-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.redco-modal-header {
    padding: 20px 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

.redco-modal-header h3 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    color: #333;
}

.redco-modal-body {
    padding: 20px;
}

.redco-modal-footer {
    padding: 0 20px 20px;
    text-align: right;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #66BB6A);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    font-weight: 500;
    color: #666;
}

.progress-steps {
    margin-top: 15px;
}

.progress-step {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    color: #666;
}

.progress-step:last-child {
    border-bottom: none;
}

.progress-step.completed {
    color: #4CAF50;
}

.progress-step.current {
    color: #333;
    font-weight: 500;
}

/* Initial Loading Screen */
.redco-diagnostic-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.95), rgba(67, 160, 71, 0.95));
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
}

.redco-diagnostic-loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.redco-loading-content {
    text-align: center;
    color: white;
    max-width: 500px;
    padding: 40px;
}

.redco-loading-spinner {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
}

.redco-spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-radius: 50%;
    animation: spinRing 2s linear infinite;
}

.redco-spinner-ring:nth-child(1) {
    border-top-color: rgba(255, 255, 255, 0.9);
    animation-delay: 0s;
}

.redco-spinner-ring:nth-child(2) {
    border-right-color: rgba(255, 255, 255, 0.7);
    animation-delay: -0.5s;
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
}

.redco-spinner-ring:nth-child(3) {
    border-bottom-color: rgba(255, 255, 255, 0.5);
    animation-delay: -1s;
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
}

.redco-loading-text h3 {
    font-size: 1.8em;
    font-weight: 600;
    margin-bottom: 16px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.redco-loading-text p {
    font-size: 1.1em;
    line-height: 1.6;
    margin-bottom: 24px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.redco-loading-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.redco-loading-dots span {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: loadingDots 1.5s ease-in-out infinite;
}

.redco-loading-dots span:nth-child(1) {
    animation-delay: 0s;
}

.redco-loading-dots span:nth-child(2) {
    animation-delay: 0.3s;
}

.redco-loading-dots span:nth-child(3) {
    animation-delay: 0.6s;
}

/* FORCE SMALLER FONT SIZE FOR DIAGNOSTIC STATISTICS VALUES */
.diagnostic-professional .redco-content-sidebar .redco-card .sidebar-stats .sidebar-stat .stat-value {
    font-size: 14px !important;
    line-height: 1.4 !important;
}

/* FORCE SMALLER FONT SIZE FOR RECENT FIXES SECTION */
.fix-date {
    font-size: 13px !important;
    line-height: 1.4 !important;
}

.fix-count {
    font-size: 13px !important;
    line-height: 1.4 !important;
}

/* More specific targeting for Recent Fixes */
.redco-card.recent-fixes-section .fix-date,
.redco-card.recent-fixes-section .fix-count,
#fix-history-list .fix-date,
#fix-history-list .fix-count {
    font-size: 13px !important;
    line-height: 1.4 !important;
}
</style>
