<?php
/**
 * WordPress Core Tweaks Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$wordpress_core_tweaks = new Redco_WordPress_Core_Tweaks();
$stats = $wordpress_core_tweaks->get_stats();
$is_enabled = redco_is_module_enabled('wordpress-core-tweaks');

// Get current settings
$current_settings = array(
    // Emoji Stripper settings
    'emoji_remove_frontend' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', true),
    'emoji_remove_admin' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_admin', false),
    'emoji_remove_feeds' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', true),
    'emoji_remove_emails' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_emails', true),

    // Version Remover settings
    'remove_css_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_versions', true),
    'remove_js_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_versions', true),
    'remove_theme_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_theme_versions', true),
    'remove_plugin_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_plugin_versions', true),
    'remove_wp_version' => redco_get_module_option('wordpress-core-tweaks', 'remove_wp_version', true),
    'exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'exclude_handles', array()),

    // Query String Remover settings
    'remove_css_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_query_strings', true),
    'remove_js_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_query_strings', true),
    'query_string_exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', array()),
    'remove_all_query_params' => redco_get_module_option('wordpress-core-tweaks', 'remove_all_query_params', false),

    // Autosave Reducer settings
    'autosave_interval' => redco_get_module_option('wordpress-core-tweaks', 'autosave_interval', 300),
    'disable_autosave' => redco_get_module_option('wordpress-core-tweaks', 'disable_autosave', false),
    'autosave_post_types' => redco_get_module_option('wordpress-core-tweaks', 'autosave_post_types', array('post', 'page')),
    'limit_revisions' => redco_get_module_option('wordpress-core-tweaks', 'limit_revisions', true),
    'max_revisions' => redco_get_module_option('wordpress-core-tweaks', 'max_revisions', 5)
);

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['redco_nonce'], 'redco_wordpress_core_tweaks_settings')) {
    // Emoji settings
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', isset($_POST['emoji_remove_frontend']));
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_admin', isset($_POST['emoji_remove_admin']));
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', isset($_POST['emoji_remove_feeds']));
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_emails', isset($_POST['emoji_remove_emails']));

    // Version removal settings
    redco_update_module_option('wordpress-core-tweaks', 'remove_css_versions', isset($_POST['remove_css_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_js_versions', isset($_POST['remove_js_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_theme_versions', isset($_POST['remove_theme_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_plugin_versions', isset($_POST['remove_plugin_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_wp_version', isset($_POST['remove_wp_version']));

    // Handle excluded handles
    $exclude_handles = isset($_POST['exclude_handles']) ? sanitize_textarea_field($_POST['exclude_handles']) : '';
    $exclude_handles_array = array_filter(array_map('trim', explode("\n", $exclude_handles)));
    redco_update_module_option('wordpress-core-tweaks', 'exclude_handles', $exclude_handles_array);

    // Query String Remover settings
    redco_update_module_option('wordpress-core-tweaks', 'remove_css_query_strings', isset($_POST['remove_css_query_strings']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_js_query_strings', isset($_POST['remove_js_query_strings']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_all_query_params', isset($_POST['remove_all_query_params']));

    // Handle query string excluded handles
    $query_exclude_handles = isset($_POST['query_string_exclude_handles']) ? sanitize_textarea_field($_POST['query_string_exclude_handles']) : '';
    $query_exclude_handles_array = array_filter(array_map('trim', explode("\n", $query_exclude_handles)));
    redco_update_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', $query_exclude_handles_array);

    // Autosave settings
    $autosave_interval = intval($_POST['autosave_interval']);
    if ($autosave_interval < 60) $autosave_interval = 60; // Minimum 1 minute
    redco_update_module_option('wordpress-core-tweaks', 'autosave_interval', $autosave_interval);
    redco_update_module_option('wordpress-core-tweaks', 'disable_autosave', isset($_POST['disable_autosave']));

    // Post types for autosave
    $post_types = isset($_POST['autosave_post_types']) ? $_POST['autosave_post_types'] : array();
    redco_update_module_option('wordpress-core-tweaks', 'autosave_post_types', $post_types);

    // Revision settings
    redco_update_module_option('wordpress-core-tweaks', 'limit_revisions', isset($_POST['limit_revisions']));
    $max_revisions = intval($_POST['max_revisions']);
    if ($max_revisions < 1) $max_revisions = 1;
    redco_update_module_option('wordpress-core-tweaks', 'max_revisions', $max_revisions);

    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'redco-optimizer') . '</p></div>';

    // Reload settings
    $current_settings = array(
        'emoji_remove_frontend' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', true),
        'emoji_remove_admin' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_admin', false),
        'emoji_remove_feeds' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', true),
        'emoji_remove_emails' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_emails', true),
        'remove_css_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_versions', true),
        'remove_js_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_versions', true),
        'remove_theme_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_theme_versions', true),
        'remove_plugin_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_plugin_versions', true),
        'remove_wp_version' => redco_get_module_option('wordpress-core-tweaks', 'remove_wp_version', true),
        'exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'exclude_handles', array()),
        'remove_css_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_query_strings', true),
        'remove_js_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_query_strings', true),
        'query_string_exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', array()),
        'remove_all_query_params' => redco_get_module_option('wordpress-core-tweaks', 'remove_all_query_params', false),
        'autosave_interval' => redco_get_module_option('wordpress-core-tweaks', 'autosave_interval', 300),
        'disable_autosave' => redco_get_module_option('wordpress-core-tweaks', 'disable_autosave', false),
        'autosave_post_types' => redco_get_module_option('wordpress-core-tweaks', 'autosave_post_types', array('post', 'page')),
        'limit_revisions' => redco_get_module_option('wordpress-core-tweaks', 'limit_revisions', true),
        'max_revisions' => redco_get_module_option('wordpress-core-tweaks', 'max_revisions', 5)
    );
}

// Get available post types
$post_types = get_post_types(array('public' => true), 'objects');
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="wordpress-core-tweaks">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-admin-tools"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('WordPress Core Tweaks', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Optimize WordPress core functionality for better performance and security', 'redco-optimizer'); ?></p>
                </div>
            </div>

        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form method="post" action="">
                    <?php wp_nonce_field('redco_wordpress_core_tweaks_settings', 'redco_nonce'); ?>

                    <!-- Emoji Removal Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-smiley"></span>
                                <?php _e('Emoji Removal', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Remove WordPress emoji scripts and styles to reduce page load time by 15-20 KB per page.', 'redco-optimizer'); ?>
                            </p>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Remove from Frontend', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="emoji_remove_frontend" value="1" <?php checked($current_settings['emoji_remove_frontend']); ?> />
                            <?php _e('Remove emoji scripts and styles from frontend pages', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Recommended for better performance.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Remove from Admin', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="emoji_remove_admin" value="1" <?php checked($current_settings['emoji_remove_admin']); ?> />
                            <?php _e('Remove emoji scripts and styles from admin area', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Only enable if you don\'t use emojis in admin.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Remove from Feeds', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="emoji_remove_feeds" value="1" <?php checked($current_settings['emoji_remove_feeds']); ?> />
                            <?php _e('Remove emoji processing from RSS feeds', 'redco-optimizer'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Remove from Emails', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="emoji_remove_emails" value="1" <?php checked($current_settings['emoji_remove_emails']); ?> />
                            <?php _e('Remove emoji processing from emails', 'redco-optimizer'); ?>
                        </label>
                    </td>
                </tr>
            </table>
                        </div>
                    </div>

                    <!-- Version Removal Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-links"></span>
                                <?php _e('Version String Removal', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Remove version query strings from CSS and JS files for better caching and security.', 'redco-optimizer'); ?>
                            </p>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('CSS Files', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_css_versions" value="1" <?php checked($current_settings['remove_css_versions']); ?> />
                            <?php _e('Remove version strings from CSS files', 'redco-optimizer'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('JavaScript Files', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_js_versions" value="1" <?php checked($current_settings['remove_js_versions']); ?> />
                            <?php _e('Remove version strings from JavaScript files', 'redco-optimizer'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Theme Files', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_theme_versions" value="1" <?php checked($current_settings['remove_theme_versions']); ?> />
                            <?php _e('Remove version strings from theme files', 'redco-optimizer'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Plugin Files', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_plugin_versions" value="1" <?php checked($current_settings['remove_plugin_versions']); ?> />
                            <?php _e('Remove version strings from plugin files', 'redco-optimizer'); ?>
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('WordPress Version', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_wp_version" value="1" <?php checked($current_settings['remove_wp_version']); ?> />
                            <?php _e('Remove WordPress version from generator tag', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Improves security by hiding WordPress version.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Exclude Handles', 'redco-optimizer'); ?></th>
                    <td>
                        <textarea name="exclude_handles" rows="4" cols="50" class="large-text"><?php echo esc_textarea(implode("\n", $current_settings['exclude_handles'])); ?></textarea>
                        <p class="description"><?php _e('Enter script/style handles to exclude from version removal (one per line).', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
            </table>
                        </div>
                    </div>

                    <!-- Query String Remover Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-links"></span>
                                <?php _e('Query String Remover', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Remove ALL query parameters from CSS and JS files for maximum caching effectiveness. More aggressive than version string removal.', 'redco-optimizer'); ?>
                            </p>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('CSS Query Strings', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_css_query_strings" value="1" <?php checked($current_settings['remove_css_query_strings']); ?> />
                            <?php _e('Remove all query parameters from CSS files', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Removes ?ver=1.0&param=value type parameters from CSS URLs.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('JavaScript Query Strings', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_js_query_strings" value="1" <?php checked($current_settings['remove_js_query_strings']); ?> />
                            <?php _e('Remove all query parameters from JavaScript files', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Removes ?ver=1.0&param=value type parameters from JS URLs.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Aggressive Mode', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_all_query_params" value="1" <?php checked($current_settings['remove_all_query_params']); ?> />
                            <?php _e('Remove ALL query parameters (including custom ones)', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Warning: This removes all query parameters, not just version strings. Use with caution.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Exclude Handles', 'redco-optimizer'); ?></th>
                    <td>
                        <textarea name="query_string_exclude_handles" rows="4" cols="50" class="large-text"><?php echo esc_textarea(implode("\n", $current_settings['query_string_exclude_handles'])); ?></textarea>
                        <p class="description"><?php _e('Enter script/style handles to exclude from query string removal (one per line).', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
            </table>
                        </div>
                    </div>

                    <!-- Autosave Optimization Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-backup"></span>
                                <?php _e('Autosave Optimization', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Optimize WordPress autosave functionality to reduce server load and improve performance.', 'redco-optimizer'); ?>
                            </p>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Autosave Control', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="disable_autosave" value="1" <?php checked($current_settings['disable_autosave']); ?> />
                            <?php _e('Disable autosave completely', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Warning: This will disable automatic saving. Make sure to save manually.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Autosave Interval', 'redco-optimizer'); ?></th>
                    <td>
                        <input type="number" name="autosave_interval" value="<?php echo esc_attr($current_settings['autosave_interval']); ?>" min="60" max="3600" />
                        <span><?php _e('seconds', 'redco-optimizer'); ?></span>
                        <p class="description">
                            <?php printf(__('WordPress default is 60 seconds. Current setting: %d seconds (%s minutes).', 'redco-optimizer'),
                                $current_settings['autosave_interval'],
                                round($current_settings['autosave_interval'] / 60, 1)
                            ); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Post Types', 'redco-optimizer'); ?></th>
                    <td>
                        <?php foreach ($post_types as $post_type): ?>
                            <label style="display: block; margin-bottom: 5px;">
                                <input type="checkbox" name="autosave_post_types[]" value="<?php echo esc_attr($post_type->name); ?>"
                                    <?php checked(in_array($post_type->name, $current_settings['autosave_post_types'])); ?> />
                                <?php echo esc_html($post_type->labels->name); ?> (<?php echo esc_html($post_type->name); ?>)
                            </label>
                        <?php endforeach; ?>
                        <p class="description"><?php _e('Select which post types should be affected by autosave optimization.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Revision Control', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="limit_revisions" value="1" <?php checked($current_settings['limit_revisions']); ?> />
                            <?php _e('Limit post revisions', 'redco-optimizer'); ?>
                        </label>
                        <br><br>
                        <label>
                            <?php _e('Maximum revisions:', 'redco-optimizer'); ?>
                            <input type="number" name="max_revisions" value="<?php echo esc_attr($current_settings['max_revisions']); ?>" min="1" max="50" style="width: 80px;" />
                        </label>
                        <p class="description"><?php _e('Limit the number of revisions stored for each post to save database space.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
            </table>
                        </div>
                    </div>

                    <p class="submit">
                        <input type="submit" name="submit" class="button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>" />
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-css-versions">
                                <span class="stat-value"><?php echo $stats['css_versions_removed'] ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('CSS Versions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-js-versions">
                                <span class="stat-value"><?php echo $stats['js_versions_removed'] ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('JS Versions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-excluded">
                                <span class="stat-value"><?php echo $stats['excluded_handles']; ?></span>
                                <span class="stat-label"><?php _e('Excluded', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-autosave">
                                <span class="stat-value"><?php echo isset($stats['current_autosave_interval']) ? round($stats['current_autosave_interval'] / 60, 1) : '0'; ?>m</span>
                                <span class="stat-label"><?php _e('Autosave', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Estimated Improvements:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><?php _e('15-20KB reduction from emoji removal', 'redco-optimizer'); ?></li>
                            <li><?php _e('Better caching from version removal', 'redco-optimizer'); ?></li>
                            <li><?php _e('Reduced server load from autosave optimization', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-admin-tools"></span>
            <h3><?php _e('WordPress Core Tweaks Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access WordPress core optimization features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>
