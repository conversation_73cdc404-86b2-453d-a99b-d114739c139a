<?php
/**
 * WordPress Core Tweaks Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$wordpress_core_tweaks = new Redco_WordPress_Core_Tweaks();
$stats = $wordpress_core_tweaks->get_stats();
$is_enabled = redco_is_module_enabled('wordpress-core-tweaks');

// Get current settings
$current_settings = array(
    // Emoji Stripper settings
    'emoji_remove_frontend' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', true),
    'emoji_remove_admin' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_admin', false),
    'emoji_remove_feeds' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', true),
    'emoji_remove_emails' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_emails', true),

    // Version Remover settings
    'remove_css_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_versions', true),
    'remove_js_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_versions', true),
    'remove_theme_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_theme_versions', true),
    'remove_plugin_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_plugin_versions', true),
    'remove_wp_version' => redco_get_module_option('wordpress-core-tweaks', 'remove_wp_version', true),
    'exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'exclude_handles', array()),

    // Query String Remover settings
    'remove_css_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_query_strings', true),
    'remove_js_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_query_strings', true),
    'query_string_exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', array()),
    'remove_all_query_params' => redco_get_module_option('wordpress-core-tweaks', 'remove_all_query_params', false),

    // Autosave Reducer settings
    'autosave_interval' => redco_get_module_option('wordpress-core-tweaks', 'autosave_interval', 300),
    'disable_autosave' => redco_get_module_option('wordpress-core-tweaks', 'disable_autosave', false),
    'autosave_post_types' => redco_get_module_option('wordpress-core-tweaks', 'autosave_post_types', array('post', 'page')),
    'limit_revisions' => redco_get_module_option('wordpress-core-tweaks', 'limit_revisions', true),
    'max_revisions' => redco_get_module_option('wordpress-core-tweaks', 'max_revisions', 5)
);

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['redco_nonce'], 'redco_wordpress_core_tweaks_settings')) {
    // Emoji settings
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', isset($_POST['emoji_remove_frontend']));
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_admin', isset($_POST['emoji_remove_admin']));
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', isset($_POST['emoji_remove_feeds']));
    redco_update_module_option('wordpress-core-tweaks', 'emoji_remove_emails', isset($_POST['emoji_remove_emails']));

    // Version removal settings
    redco_update_module_option('wordpress-core-tweaks', 'remove_css_versions', isset($_POST['remove_css_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_js_versions', isset($_POST['remove_js_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_theme_versions', isset($_POST['remove_theme_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_plugin_versions', isset($_POST['remove_plugin_versions']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_wp_version', isset($_POST['remove_wp_version']));

    // Handle excluded handles
    $exclude_handles = isset($_POST['exclude_handles']) ? sanitize_textarea_field($_POST['exclude_handles']) : '';
    $exclude_handles_array = array_filter(array_map('trim', explode("\n", $exclude_handles)));
    redco_update_module_option('wordpress-core-tweaks', 'exclude_handles', $exclude_handles_array);

    // Query String Remover settings
    redco_update_module_option('wordpress-core-tweaks', 'remove_css_query_strings', isset($_POST['remove_css_query_strings']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_js_query_strings', isset($_POST['remove_js_query_strings']));
    redco_update_module_option('wordpress-core-tweaks', 'remove_all_query_params', isset($_POST['remove_all_query_params']));

    // Handle query string excluded handles
    $query_exclude_handles = isset($_POST['query_string_exclude_handles']) ? sanitize_textarea_field($_POST['query_string_exclude_handles']) : '';
    $query_exclude_handles_array = array_filter(array_map('trim', explode("\n", $query_exclude_handles)));
    redco_update_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', $query_exclude_handles_array);

    // Autosave settings
    $autosave_interval = intval($_POST['autosave_interval']);
    if ($autosave_interval < 60) $autosave_interval = 60; // Minimum 1 minute
    redco_update_module_option('wordpress-core-tweaks', 'autosave_interval', $autosave_interval);
    redco_update_module_option('wordpress-core-tweaks', 'disable_autosave', isset($_POST['disable_autosave']));

    // Post types for autosave
    $post_types = isset($_POST['autosave_post_types']) ? $_POST['autosave_post_types'] : array();
    redco_update_module_option('wordpress-core-tweaks', 'autosave_post_types', $post_types);

    // Revision settings
    redco_update_module_option('wordpress-core-tweaks', 'limit_revisions', isset($_POST['limit_revisions']));
    $max_revisions = intval($_POST['max_revisions']);
    if ($max_revisions < 1) $max_revisions = 1;
    redco_update_module_option('wordpress-core-tweaks', 'max_revisions', $max_revisions);

    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'redco-optimizer') . '</p></div>';

    // Reload settings
    $current_settings = array(
        'emoji_remove_frontend' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', true),
        'emoji_remove_admin' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_admin', false),
        'emoji_remove_feeds' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', true),
        'emoji_remove_emails' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_emails', true),
        'remove_css_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_versions', true),
        'remove_js_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_versions', true),
        'remove_theme_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_theme_versions', true),
        'remove_plugin_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_plugin_versions', true),
        'remove_wp_version' => redco_get_module_option('wordpress-core-tweaks', 'remove_wp_version', true),
        'exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'exclude_handles', array()),
        'remove_css_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_query_strings', true),
        'remove_js_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_query_strings', true),
        'query_string_exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', array()),
        'remove_all_query_params' => redco_get_module_option('wordpress-core-tweaks', 'remove_all_query_params', false),
        'autosave_interval' => redco_get_module_option('wordpress-core-tweaks', 'autosave_interval', 300),
        'disable_autosave' => redco_get_module_option('wordpress-core-tweaks', 'disable_autosave', false),
        'autosave_post_types' => redco_get_module_option('wordpress-core-tweaks', 'autosave_post_types', array('post', 'page')),
        'limit_revisions' => redco_get_module_option('wordpress-core-tweaks', 'limit_revisions', true),
        'max_revisions' => redco_get_module_option('wordpress-core-tweaks', 'max_revisions', 5)
    );
}

// Get available post types
$post_types = get_post_types(array('public' => true), 'objects');
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="wordpress-core-tweaks">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-admin-tools"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('WordPress Core Tweaks', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Optimize WordPress core functionality for better performance and security', 'redco-optimizer'); ?></p>
                </div>
            </div>

        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form method="post" action="">
                    <?php wp_nonce_field('redco_wordpress_core_tweaks_settings', 'redco_nonce'); ?>

                    <!-- Emoji Removal Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-smiley"></span>
                                <?php _e('WordPress Emoji Optimization', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="enable-all-emoji-removal">
                                    <?php _e('Remove All Emojis', 'redco-optimizer'); ?>
                                </button>
                                <button type="button" class="button button-small" id="reset-emoji-settings">
                                    <?php _e('Reset to Defaults', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('WordPress includes emoji support that loads additional scripts and styles on every page. Removing these can reduce page size by 15-20KB and improve loading speed.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Performance Savings:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="emoji-impact"><?php _e('15-20KB per page', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php _e('Emoji Removal Options', 'redco-optimizer'); ?>
                                </h4>

                                <div class="emoji-options">
                                    <div class="emoji-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="emoji_remove_frontend" value="1" <?php checked($current_settings['emoji_remove_frontend']); ?> class="emoji-checkbox">
                                            <span class="option-icon">🌐</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Frontend Pages', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji scripts and styles from all public-facing pages. This provides the biggest performance benefit for your visitors.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Size Reduction:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high">15-20KB</span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('HTTP Requests:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high">-2</span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="emoji-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="emoji_remove_admin" value="1" <?php checked($current_settings['emoji_remove_admin']); ?> class="emoji-checkbox">
                                            <span class="option-icon">⚙️</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Admin Area', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji support from WordPress admin area. Only enable if you don\'t use emojis in posts, comments, or admin interface.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Admin Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Good', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="emoji-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="emoji_remove_feeds" value="1" <?php checked($current_settings['emoji_remove_feeds']); ?> class="emoji-checkbox">
                                            <span class="option-icon">📡</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('RSS Feeds', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji processing from RSS feeds. Improves feed performance and compatibility with feed readers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Feed Size:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Reduced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="emoji-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="emoji_remove_emails" value="1" <?php checked($current_settings['emoji_remove_emails']); ?> class="emoji-checkbox">
                                            <span class="option-icon">📧</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Email Processing', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji processing from WordPress emails. Improves email delivery speed and reduces server processing time.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Email Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Faster', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Server Load:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Reduced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="emoji-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="estimated-savings"><?php _e('Estimated savings: 0KB', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="emoji-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Note: Removing emoji support will not affect existing emojis in your content, but new emojis may not display correctly.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Version Removal Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-links"></span>
                                <?php _e('Version String & Security Optimization', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="enable-all-version-removal">
                                    <?php _e('Enable All Security', 'redco-optimizer'); ?>
                                </button>
                                <button type="button" class="button button-small" id="reset-version-settings">
                                    <?php _e('Reset Versions', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Remove version query strings from assets and hide WordPress version information to improve caching effectiveness and enhance security by reducing information disclosure.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Security & Caching Benefits:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="version-impact"><?php _e('High Security', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-shield"></span>
                                    <?php _e('Asset Version Removal', 'redco-optimizer'); ?>
                                </h4>

                                <div class="version-options">
                                    <div class="version-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="remove_css_versions" value="1" <?php checked($current_settings['remove_css_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">🎨</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('CSS Files', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version parameters from CSS files (e.g., style.css?ver=1.0). Improves browser caching and CDN effectiveness.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('CDN Friendly:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Yes', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="remove_js_versions" value="1" <?php checked($current_settings['remove_js_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">⚡</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('JavaScript Files', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version parameters from JavaScript files (e.g., script.js?ver=1.0). Enhances caching and reduces cache-busting issues.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Better', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="remove_theme_versions" value="1" <?php checked($current_settings['remove_theme_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">🎭</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Theme Files', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version strings specifically from theme assets. Useful for custom themes with frequent updates.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Theme Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Good', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="remove_plugin_versions" value="1" <?php checked($current_settings['remove_plugin_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">🔌</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Plugin Files', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version strings from plugin assets. May affect some plugins that rely on version parameters for functionality.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Plugin Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Good', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option security">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="remove_wp_version" value="1" <?php checked($current_settings['remove_wp_version']); ?> class="version-checkbox">
                                            <span class="option-icon">🛡️</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('WordPress Version Generator', 'redco-optimizer'); ?></strong>
                                                    <span class="security-badge"><?php _e('Security', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes WordPress version from HTML meta generator tag. Improves security by hiding version information from potential attackers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Security:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Information Hiding:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Yes', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="exclusion-settings">
                                    <h5 class="exclusion-title">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('Advanced Exclusion Settings', 'redco-optimizer'); ?>
                                    </h5>
                                    <div class="exclusion-content">
                                        <label for="exclude_handles" class="exclusion-label">
                                            <strong><?php _e('Exclude Specific Handles', 'redco-optimizer'); ?></strong>
                                            <span class="exclusion-description"><?php _e('Script/style handles to exclude from version removal', 'redco-optimizer'); ?></span>
                                        </label>
                                        <textarea name="exclude_handles" id="exclude_handles" rows="4" class="exclusion-textarea" placeholder="<?php _e('Enter handles one per line, e.g.:\njquery\nbootstrap\ncustom-script', 'redco-optimizer'); ?>"><?php echo esc_textarea(implode("\n", $current_settings['exclude_handles'])); ?></textarea>
                                        <div class="exclusion-help">
                                            <span class="dashicons dashicons-info"></span>
                                            <?php _e('Use this to exclude specific scripts or styles that may break when version parameters are removed.', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="version-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="security-level"><?php _e('Security level: Basic', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="version-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Version removal improves caching but may affect some plugins. Test thoroughly after enabling.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Query String Remover Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-links"></span>
                                <?php _e('Query String Remover', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Remove ALL query parameters from CSS and JS files for maximum caching effectiveness. More aggressive than version string removal.', 'redco-optimizer'); ?>
                            </p>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('CSS Query Strings', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_css_query_strings" value="1" <?php checked($current_settings['remove_css_query_strings']); ?> />
                            <?php _e('Remove all query parameters from CSS files', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Removes ?ver=1.0&param=value type parameters from CSS URLs.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('JavaScript Query Strings', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_js_query_strings" value="1" <?php checked($current_settings['remove_js_query_strings']); ?> />
                            <?php _e('Remove all query parameters from JavaScript files', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Removes ?ver=1.0&param=value type parameters from JS URLs.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Aggressive Mode', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="remove_all_query_params" value="1" <?php checked($current_settings['remove_all_query_params']); ?> />
                            <?php _e('Remove ALL query parameters (including custom ones)', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Warning: This removes all query parameters, not just version strings. Use with caution.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Exclude Handles', 'redco-optimizer'); ?></th>
                    <td>
                        <textarea name="query_string_exclude_handles" rows="4" cols="50" class="large-text"><?php echo esc_textarea(implode("\n", $current_settings['query_string_exclude_handles'])); ?></textarea>
                        <p class="description"><?php _e('Enter script/style handles to exclude from query string removal (one per line).', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
            </table>
                        </div>
                    </div>

                    <!-- Autosave Optimization Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-backup"></span>
                                <?php _e('Autosave Optimization', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Optimize WordPress autosave functionality to reduce server load and improve performance.', 'redco-optimizer'); ?>
                            </p>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Autosave Control', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="disable_autosave" value="1" <?php checked($current_settings['disable_autosave']); ?> />
                            <?php _e('Disable autosave completely', 'redco-optimizer'); ?>
                        </label>
                        <p class="description"><?php _e('Warning: This will disable automatic saving. Make sure to save manually.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Autosave Interval', 'redco-optimizer'); ?></th>
                    <td>
                        <input type="number" name="autosave_interval" value="<?php echo esc_attr($current_settings['autosave_interval']); ?>" min="60" max="3600" />
                        <span><?php _e('seconds', 'redco-optimizer'); ?></span>
                        <p class="description">
                            <?php printf(__('WordPress default is 60 seconds. Current setting: %d seconds (%s minutes).', 'redco-optimizer'),
                                $current_settings['autosave_interval'],
                                round($current_settings['autosave_interval'] / 60, 1)
                            ); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Post Types', 'redco-optimizer'); ?></th>
                    <td>
                        <?php foreach ($post_types as $post_type): ?>
                            <label style="display: block; margin-bottom: 5px;">
                                <input type="checkbox" name="autosave_post_types[]" value="<?php echo esc_attr($post_type->name); ?>"
                                    <?php checked(in_array($post_type->name, $current_settings['autosave_post_types'])); ?> />
                                <?php echo esc_html($post_type->labels->name); ?> (<?php echo esc_html($post_type->name); ?>)
                            </label>
                        <?php endforeach; ?>
                        <p class="description"><?php _e('Select which post types should be affected by autosave optimization.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Revision Control', 'redco-optimizer'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="limit_revisions" value="1" <?php checked($current_settings['limit_revisions']); ?> />
                            <?php _e('Limit post revisions', 'redco-optimizer'); ?>
                        </label>
                        <br><br>
                        <label>
                            <?php _e('Maximum revisions:', 'redco-optimizer'); ?>
                            <input type="number" name="max_revisions" value="<?php echo esc_attr($current_settings['max_revisions']); ?>" min="1" max="50" style="width: 80px;" />
                        </label>
                        <p class="description"><?php _e('Limit the number of revisions stored for each post to save database space.', 'redco-optimizer'); ?></p>
                    </td>
                </tr>
            </table>
                        </div>
                    </div>

                    <p class="submit">
                        <input type="submit" name="submit" class="button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>" />
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-css-versions">
                                <span class="stat-value"><?php echo $stats['css_versions_removed'] ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('CSS Versions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-js-versions">
                                <span class="stat-value"><?php echo $stats['js_versions_removed'] ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('JS Versions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-excluded">
                                <span class="stat-value"><?php echo $stats['excluded_handles']; ?></span>
                                <span class="stat-label"><?php _e('Excluded', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-autosave">
                                <span class="stat-value"><?php echo isset($stats['current_autosave_interval']) ? round($stats['current_autosave_interval'] / 60, 1) : '0'; ?>m</span>
                                <span class="stat-label"><?php _e('Autosave', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Estimated Improvements:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><?php _e('15-20KB reduction from emoji removal', 'redco-optimizer'); ?></li>
                            <li><?php _e('Better caching from version removal', 'redco-optimizer'); ?></li>
                            <li><?php _e('Reduced server load from autosave optimization', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-admin-tools"></span>
            <h3><?php _e('WordPress Core Tweaks Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access WordPress core optimization features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // WordPress Core Tweaks Enhanced Functionality

    // Update emoji summary
    function updateEmojiSummary() {
        const emojiCheckboxes = $('.emoji-checkbox:checked');
        const enabledCount = emojiCheckboxes.length;

        $('.emoji-summary .enabled-count').text(enabledCount + ' <?php _e("optimizations enabled", "redco-optimizer"); ?>');

        // Calculate estimated savings
        let savings = 0;
        if ($('input[name="emoji_remove_frontend"]').is(':checked')) {
            savings += 18; // 15-20KB average
        }
        if ($('input[name="emoji_remove_admin"]').is(':checked')) {
            savings += 5;
        }
        if ($('input[name="emoji_remove_feeds"]').is(':checked')) {
            savings += 2;
        }
        if ($('input[name="emoji_remove_emails"]').is(':checked')) {
            savings += 1;
        }

        $('.emoji-summary .estimated-savings').text('<?php _e("Estimated savings:", "redco-optimizer"); ?> ' + savings + 'KB');

        // Update impact indicator
        let impactText = savings > 15 ? '<?php _e("15-20KB per page", "redco-optimizer"); ?>' :
                        savings > 5 ? '<?php _e("5-15KB per page", "redco-optimizer"); ?>' :
                        '<?php _e("Minimal savings", "redco-optimizer"); ?>';
        $('#emoji-impact').text(impactText);
    }

    // Update version summary
    function updateVersionSummary() {
        const versionCheckboxes = $('.version-checkbox:checked');
        const enabledCount = versionCheckboxes.length;

        $('.version-summary .enabled-count').text(enabledCount + ' <?php _e("optimizations enabled", "redco-optimizer"); ?>');

        // Calculate security level
        let securityLevel = '<?php _e("Basic", "redco-optimizer"); ?>';
        if ($('input[name="remove_wp_version"]').is(':checked')) {
            securityLevel = enabledCount >= 3 ? '<?php _e("High Security", "redco-optimizer"); ?>' : '<?php _e("Medium Security", "redco-optimizer"); ?>';
        } else if (enabledCount >= 2) {
            securityLevel = '<?php _e("Medium Security", "redco-optimizer"); ?>';
        }

        $('.version-summary .security-level').text('<?php _e("Security level:", "redco-optimizer"); ?> ' + securityLevel);

        // Update impact indicator
        $('#version-impact').text(securityLevel);
    }

    // Enable All Emoji Removal button
    $('#enable-all-emoji-removal').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const allEmojiCheckboxes = $('.emoji-checkbox');
        const allChecked = allEmojiCheckboxes.length === allEmojiCheckboxes.filter(':checked').length;

        button.addClass('loading').text('<?php _e("Processing...", "redco-optimizer"); ?>');

        setTimeout(() => {
            if (allChecked) {
                // Disable all
                allEmojiCheckboxes.prop('checked', false);
                button.text('<?php _e("Remove All Emojis", "redco-optimizer"); ?>');
                button.removeClass('button-primary').addClass('button-secondary');
            } else {
                // Enable all
                allEmojiCheckboxes.prop('checked', true);
                button.text('<?php _e("Keep All Emojis", "redco-optimizer"); ?>');
                button.removeClass('button-secondary').addClass('button-primary');
            }

            updateEmojiSummary();
            button.removeClass('loading');

            // Visual feedback
            $('.emoji-option').addClass('selection-highlight');
            setTimeout(() => {
                $('.emoji-option').removeClass('selection-highlight');
            }, 300);

            showCoreNotification('<?php _e("Emoji settings updated successfully!", "redco-optimizer"); ?>', 'success');
        }, 500);
    });

    // Reset Emoji Settings button
    $('#reset-emoji-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to defaults (frontend, feeds, emails enabled; admin disabled)
            $('input[name="emoji_remove_frontend"]').prop('checked', true);
            $('input[name="emoji_remove_admin"]').prop('checked', false);
            $('input[name="emoji_remove_feeds"]').prop('checked', true);
            $('input[name="emoji_remove_emails"]').prop('checked', true);

            updateEmojiSummary();
            button.removeClass('loading').text('<?php _e("Reset to Defaults", "redco-optimizer"); ?>');

            showCoreNotification('<?php _e("Emoji settings reset to recommended defaults", "redco-optimizer"); ?>', 'info');
        }, 500);
    });

    // Enable All Version Removal button
    $('#enable-all-version-removal').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const allVersionCheckboxes = $('.version-checkbox');
        const allChecked = allVersionCheckboxes.length === allVersionCheckboxes.filter(':checked').length;

        button.addClass('loading').text('<?php _e("Processing...", "redco-optimizer"); ?>');

        setTimeout(() => {
            if (allChecked) {
                // Disable all
                allVersionCheckboxes.prop('checked', false);
                button.text('<?php _e("Enable All Security", "redco-optimizer"); ?>');
                button.removeClass('button-primary').addClass('button-secondary');
            } else {
                // Enable all
                allVersionCheckboxes.prop('checked', true);
                button.text('<?php _e("Disable All Security", "redco-optimizer"); ?>');
                button.removeClass('button-secondary').addClass('button-primary');
            }

            updateVersionSummary();
            button.removeClass('loading');

            // Visual feedback
            $('.version-option').addClass('selection-highlight');
            setTimeout(() => {
                $('.version-option').removeClass('selection-highlight');
            }, 300);

            showCoreNotification('<?php _e("Security settings updated successfully!", "redco-optimizer"); ?>', 'success');
        }, 500);
    });

    // Reset Version Settings button
    $('#reset-version-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to defaults (CSS and JS enabled, others disabled)
            $('input[name="remove_css_versions"]').prop('checked', true);
            $('input[name="remove_js_versions"]').prop('checked', true);
            $('input[name="remove_theme_versions"]').prop('checked', false);
            $('input[name="remove_plugin_versions"]').prop('checked', false);
            $('input[name="remove_wp_version"]').prop('checked', true);

            updateVersionSummary();
            button.removeClass('loading').text('<?php _e("Reset Versions", "redco-optimizer"); ?>');

            showCoreNotification('<?php _e("Version settings reset to recommended defaults", "redco-optimizer"); ?>', 'info');
        }, 500);
    });

    // Individual checkbox changes
    $('.emoji-checkbox').on('change', function() {
        updateEmojiSummary();

        // Update button states
        const allEmojiCheckboxes = $('.emoji-checkbox');
        const allChecked = allEmojiCheckboxes.length === allEmojiCheckboxes.filter(':checked').length;

        if (allChecked) {
            $('#enable-all-emoji-removal').text('<?php _e("Keep All Emojis", "redco-optimizer"); ?>');
            $('#enable-all-emoji-removal').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#enable-all-emoji-removal').text('<?php _e("Remove All Emojis", "redco-optimizer"); ?>');
            $('#enable-all-emoji-removal').removeClass('button-primary').addClass('button-secondary');
        }
    });

    $('.version-checkbox').on('change', function() {
        updateVersionSummary();

        // Update button states
        const allVersionCheckboxes = $('.version-checkbox');
        const allChecked = allVersionCheckboxes.length === allVersionCheckboxes.filter(':checked').length;

        if (allChecked) {
            $('#enable-all-version-removal').text('<?php _e("Disable All Security", "redco-optimizer"); ?>');
            $('#enable-all-version-removal').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#enable-all-version-removal').text('<?php _e("Enable All Security", "redco-optimizer"); ?>');
            $('#enable-all-version-removal').removeClass('button-primary').addClass('button-secondary');
        }
    });

    // Notification system
    function showCoreNotification(message, type) {
        const notification = $('<div class="core-tweaks-notification ' + type + '">' + message + '</div>');
        $('body').append(notification);

        setTimeout(() => {
            notification.addClass('show');
        }, 100);

        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Initialize
    updateEmojiSummary();
    updateVersionSummary();

    console.log('✅ WordPress Core Tweaks enhanced functionality loaded');
});
</script>
