<?php
/**
 * CSS/JS Minifier Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$minifier = new Redco_CSS_JS_Minifier();
$is_enabled = redco_is_module_enabled('css-js-minifier');

// Get current settings
$minify_css = redco_get_module_option('css-js-minifier', 'minify_css', true);
$minify_js = redco_get_module_option('css-js-minifier', 'minify_js', true);
$minify_inline = redco_get_module_option('css-js-minifier', 'minify_inline', true);
$exclude_css = redco_get_module_option('css-js-minifier', 'exclude_css', array());
$exclude_js = redco_get_module_option('css-js-minifier', 'exclude_js', array());

// Get enqueued styles and scripts
$enqueued_styles = redco_get_enqueued_styles();
$enqueued_scripts = redco_get_enqueued_scripts();

// Get statistics - only if module is enabled
$stats = array('files_minified' => 0, 'bytes_saved' => 0);
if ($is_enabled && class_exists('Redco_CSS_JS_Minifier')) {
    $stats = $minifier->get_stats();
}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="css-js-minifier">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-media-code"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('CSS/JS Minifier', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Optimize CSS and JavaScript files by removing unnecessary whitespace and comments', 'redco-optimizer'); ?></p>
                </div>
            </div>

        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="css-js-minifier">
                    <!-- Minification Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('Minification Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('CSS Minification', 'redco-optimizer'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="settings[minify_css]" value="1" <?php checked($minify_css); ?>>
                                            <?php _e('Enable CSS minification', 'redco-optimizer'); ?>
                                        </label>
                                        <p class="description">
                                            <?php _e('Minify CSS files to reduce file sizes and improve load times.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('JavaScript Minification', 'redco-optimizer'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="settings[minify_js]" value="1" <?php checked($minify_js); ?>>
                                            <?php _e('Enable JavaScript minification', 'redco-optimizer'); ?>
                                        </label>
                                        <p class="description">
                                            <?php _e('Minify JavaScript files to reduce file sizes and improve load times.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Inline Minification', 'redco-optimizer'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="settings[minify_inline]" value="1" <?php checked($minify_inline); ?>>
                                            <?php _e('Minify inline CSS and JavaScript', 'redco-optimizer'); ?>
                                        </label>
                                        <p class="description">
                                            <?php _e('Minify CSS and JavaScript code embedded directly in HTML.', 'redco-optimizer'); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($enqueued_styles)): ?>
                    <!-- Exclude CSS Files Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-appearance"></span>
                                <?php _e('Exclude CSS Files', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Select CSS files that should not be minified (e.g., files that break when minified).', 'redco-optimizer'); ?>
                            </p>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('CSS Files to Exclude', 'redco-optimizer'); ?></th>
                                    <td>
                                        <div class="checkbox-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                            <?php foreach ($enqueued_styles as $handle => $style): ?>
                                                <div class="checkbox-item" style="margin-bottom: 8px;">
                                                    <label>
                                                        <input type="checkbox" name="settings[exclude_css][]"
                                                               value="<?php echo esc_attr($handle); ?>"
                                                               <?php checked(in_array($handle, $exclude_css)); ?>>
                                                        <strong><?php echo esc_html($handle); ?></strong>
                                                        <?php if (!empty($style['src'])): ?>
                                                            <br><small style="color: #666;"><?php echo esc_html(basename($style['src'])); ?></small>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <p class="description">
                                            <?php echo sprintf(__('%d CSS files found. Select files to exclude from minification.', 'redco-optimizer'), count($enqueued_styles)); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($enqueued_scripts)): ?>
                    <!-- Exclude JavaScript Files Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-media-code"></span>
                                <?php _e('Exclude JavaScript Files', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Select JavaScript files that should not be minified (e.g., files that break when minified).', 'redco-optimizer'); ?>
                            </p>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('JavaScript Files to Exclude', 'redco-optimizer'); ?></th>
                                    <td>
                                        <div class="checkbox-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                            <?php foreach ($enqueued_scripts as $handle => $script): ?>
                                                <div class="checkbox-item" style="margin-bottom: 8px;">
                                                    <label>
                                                        <input type="checkbox" name="settings[exclude_js][]"
                                                               value="<?php echo esc_attr($handle); ?>"
                                                               <?php checked(in_array($handle, $exclude_js)); ?>>
                                                        <strong><?php echo esc_html($handle); ?></strong>
                                                        <?php if (!empty($script['src'])): ?>
                                                            <br><small style="color: #666;"><?php echo esc_html(basename($script['src'])); ?></small>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <p class="description">
                                            <?php echo sprintf(__('%d JavaScript files found. Select files to exclude from minification.', 'redco-optimizer'), count($enqueued_scripts)); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>

                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
                    </p>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-files">
                                <span class="stat-value"><?php echo number_format($stats['files_minified']); ?></span>
                                <span class="stat-label"><?php _e('Files Minified', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-saved">
                                <span class="stat-value"><?php echo redco_format_bytes($stats['bytes_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bytes Saved', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-css">
                                <span class="stat-value"><?php echo $minify_css ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('CSS Minify', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-js">
                                <span class="stat-value"><?php echo $minify_js ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('JS Minify', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Cache Management', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="clear-minified-cache" class="button button-secondary" data-redco-action="clear_minified_cache" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-trash"></span>
                            <?php _e('Clear Minified Cache', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Clear all minified files. New minified versions will be generated when pages are visited.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-media-code"></span>
            <h3><?php _e('CSS/JS Minifier Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access CSS and JavaScript minification features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>
