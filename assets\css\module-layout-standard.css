/**
 * Standardized Module Layout CSS
 * Based on Diagnostic & Auto-Fix module design
 * Redco Optimizer Plugin
 */

/* CSS Variables for consistent theming */
:root {
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-secondary: #64748b;
    --redco-success: #4CAF50;
    --redco-warning: #f59e0b;
    --redco-danger: #ef4444;
    --redco-info: #2563eb;
    --redco-light: #f8fafc;
    --redco-dark: #1e293b;
    --redco-border: #e2e8f0;
    --redco-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --redco-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    /* WordPress Admin Bar Height - Standard WordPress CSS Custom Property */
    --wp-admin--admin-bar--height: 32px;
}

/* Module Tab Container */
.redco-module-tab {
    background: #f9fafb;
    min-height: 100vh;
    padding: 0px 0px 50px 0px;
    margin: 0;
}

/* Professional Header Section */
.module-header-section {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    margin: 0 -20px 30px -20px;
    padding: 20px 40px 20px 50px;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    min-height: 163px;
}

.header-content {
    max-width: 1200px;
    max-height: 200px;
    margin: 0 auto;
    padding: 30px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
}

.header-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-icon .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: white;
}

.header-text h1 {
    color: white;
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2;
}

.header-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 16px;
    line-height: 1.4;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Module Toggle in Header */
.module-toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.module-toggle-wrapper label {
    color: white;
    font-weight: 500;
    margin: 0;
    cursor: pointer;
}

/* Main Layout */
.redco-module-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.module-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.redco-content-main {
    flex: 1;
    min-width: 0;
}

.redco-content-sidebar {
    width: 260px;
    flex-shrink: 0;
}

/* Card Styling */
.redco-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.redco-card:hover {
    box-shadow: var(--redco-shadow-lg);
}

.card-header {
    background: #fafbfc;
    border-bottom: 1px solid var(--redco-border);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.card-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.card-content {
    padding: 24px;
}

/* Sidebar Sections */
.redco-sidebar-section {
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 20px;
    overflow: hidden;
}

.sidebar-section-header {
    background: #fafbfc;
    border-bottom: 1px solid var(--redco-border);
    padding: 12px 16px;
}

.sidebar-section-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
    display: flex;
    align-items: center;
    gap: 6px;
}

.sidebar-section-header h3 .dashicons {
    color: #4caf50;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.sidebar-section-content {
    padding: 12px 16px;
}

.sidebar-section-content .description {
    font-size: 0.7em;
    color: #646970;
    margin-top: 6px;
    margin-bottom: 0;
    line-height: 1.3;
}

/* Statistics Display */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-primary);
    margin-bottom: 4px;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: var(--redco-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Enhanced Form Elements */
.form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.form-table th {
    width: 220px;
    padding: 20px 15px 20px 0;
    text-align: left;
    font-weight: 600;
    color: var(--redco-dark);
    vertical-align: top;
    font-size: 14px;
    line-height: 1.4;
}

.form-table td {
    padding: 20px 0;
    vertical-align: top;
    line-height: 1.5;
}

.form-table tr {
    border-bottom: 1px solid #f0f0f1;
    transition: background-color 0.2s ease;
}

.form-table tr:last-child {
    border-bottom: none;
}

.form-table tr:hover {
    background-color: #f8f9fa;
}

/* Enhanced Checkbox Styling */
.form-table td label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    font-weight: 500;
    color: var(--redco-dark);
    cursor: pointer;
    line-height: 1.4;
    transition: color 0.2s ease;
}

.form-table td label:hover {
    color: var(--redco-primary);
}

.form-table td input[type="checkbox"] {
    margin: 2px 0 0 0;
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    accent-color: var(--redco-primary);
}

.form-table td .description {
    margin-top: 8px;
    color: var(--redco-secondary);
    font-size: 13px;
    line-height: 1.5;
    font-style: normal;
}

/* Settings Sections */
.redco-settings-section {
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 24px;
    overflow: hidden;
}

.redco-settings-section h3 {
    background: #fafbfc;
    border-bottom: 1px solid var(--redco-border);
    padding: 20px 24px;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.redco-settings-section h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.redco-settings-section .form-table {
    margin: 0;
}

.redco-settings-section .form-table th,
.redco-settings-section .form-table td {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.redco-settings-section .form-table tr:last-child th,
.redco-settings-section .form-table tr:last-child td {
    border-bottom: none;
}

/* Button Styling */
.button-primary {
    background: var(--redco-primary) !important;
    border-color: var(--redco-primary) !important;
    color: white !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.button-primary:hover {
    background: var(--redco-primary-dark) !important;
    border-color: var(--redco-primary-dark) !important;
    transform: translateY(-1px) !important;
}

.button-secondary {
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.button-secondary:hover {
    transform: translateY(-1px) !important;
}

/* Module Disabled State */
.redco-module-disabled {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin: 20px auto;
    max-width: 600px;
}

.disabled-message .dashicons {
    font-size: 48px;
    color: var(--redco-secondary);
    margin-bottom: 20px;
}

.disabled-message h3 {
    font-size: 24px;
    color: var(--redco-dark);
    margin-bottom: 16px;
}

.disabled-message p {
    color: var(--redco-secondary);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .module-layout {
        flex-direction: column;
    }

    .redco-content-sidebar {
        width: 100%;
        margin-top: 0;
    }

    .header-content {
        padding: 30px 20px 0 20px;
        max-height: 200px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        padding: 30px 10px 0 10px;
        max-height: 200px;
    }

    .header-main {
        flex-direction: column;
        text-align: center;
    }

    .header-text h1 {
        font-size: 24px;
    }

    .redco-module-content {
        padding: 0 10px;
    }

    .module-header-section {
        margin: 0 -10px 20px -10px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .card-content {
        padding: 16px;
    }

    .sidebar-section-header {
        padding: 16px 20px;
    }

    .sidebar-section-content {
        padding: 16px;
    }

    .card-header {
        padding: 16px 20px;
    }

    .redco-settings-section h3 {
        padding: 16px 20px;
    }

    .redco-settings-section .form-table th,
    .redco-settings-section .form-table td {
        padding: 16px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Enhanced File Exclusion Lists */
.checkbox-list {
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    max-height: 250px;
    overflow-y: auto;
}

.checkbox-list .checkbox-item {
    margin-bottom: 12px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.checkbox-list .checkbox-item:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.checkbox-list .checkbox-item:last-child {
    margin-bottom: 0;
}

.checkbox-list .checkbox-item label {
    margin: 0;
    font-weight: 500;
    color: var(--redco-dark);
    cursor: pointer;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.checkbox-list .checkbox-item input[type="checkbox"] {
    margin: 2px 0 0 0;
    flex-shrink: 0;
    accent-color: var(--redco-primary);
}

.checkbox-list .checkbox-item small {
    color: var(--redco-secondary);
    font-size: 12px;
    margin-top: 2px;
    display: block;
}

/* Enhanced Statistics Display */
.stat-item {
    text-align: center;
    padding: 20px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--redco-primary);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-dark));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--redco-primary);
    margin-bottom: 6px;
    display: block;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 11px;
    color: var(--redco-secondary);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-weight: 600;
    line-height: 1.2;
}

/* Enhanced Button Styling */
.redco-sidebar-section .button {
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-decoration: none !important;
}

.redco-sidebar-section .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.redco-sidebar-section .button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Card Headers */
.card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f4f6f8 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 20px 24px;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--redco-primary), transparent);
    opacity: 0.3;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

/* Enhanced Cache Statistics Sections - Compact Design */

/* Compact Cache Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 8px;
}

.stat-item {
    text-align: center;
    padding: 8px 6px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.stat-item:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    margin-bottom: 2px;
    display: block;
    line-height: 1.2;
}

.stat-label {
    font-size: 10px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-weight: 500;
    line-height: 1.1;
}

/* Individual Cache Statistics Colors - High Contrast */
.stat-hits .stat-value {
    color: #2e7d32 !important; /* Dark green for successful hits */
    font-weight: 700 !important;
}

.stat-hits .stat-label {
    color: #1b5e20 !important; /* Darker green for label */
}

.stat-hits:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.stat-misses .stat-value {
    color: #e65100 !important; /* Dark orange for misses */
    font-weight: 700 !important;
}

.stat-misses .stat-label {
    color: #bf360c !important; /* Darker orange for label */
}

.stat-misses:hover {
    border-color: #ff9800;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.stat-size .stat-value {
    color: #1565c0 !important; /* Dark blue for cache size */
    font-weight: 700 !important;
}

.stat-size .stat-label {
    color: #0d47a1 !important; /* Darker blue for label */
}

.stat-size:hover {
    border-color: #2196f3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.stat-ratio .stat-value {
    color: #6a1b9a !important; /* Dark purple for hit ratio */
    font-weight: 700 !important;
}

.stat-ratio .stat-label {
    color: #4a148c !important; /* Darker purple for label */
}

.stat-ratio:hover {
    border-color: #9c27b0;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.2);
}

/* Lazy Load Module Statistics Colors */
.stat-images .stat-value {
    color: #2e7d32 !important; /* Dark green for images processed */
    font-weight: 700 !important;
}

.stat-images .stat-label {
    color: #1b5e20 !important;
}

.stat-bandwidth .stat-value {
    color: #1565c0 !important; /* Dark blue for bandwidth saved */
    font-weight: 700 !important;
}

.stat-bandwidth .stat-label {
    color: #0d47a1 !important;
}

.stat-threshold .stat-value {
    color: #e65100 !important; /* Dark orange for threshold */
    font-weight: 700 !important;
}

.stat-threshold .stat-label {
    color: #bf360c !important;
}

.stat-featured .stat-value {
    color: #6a1b9a !important; /* Dark purple for featured setting */
    font-weight: 700 !important;
}

.stat-featured .stat-label {
    color: #4a148c !important;
}

/* CSS/JS Minifier Module Statistics Colors */
.stat-files .stat-value {
    color: #2e7d32 !important; /* Dark green for files minified */
    font-weight: 700 !important;
}

.stat-files .stat-label {
    color: #1b5e20 !important;
}

.stat-saved .stat-value {
    color: #1565c0 !important; /* Dark blue for bytes saved */
    font-weight: 700 !important;
}

.stat-saved .stat-label {
    color: #0d47a1 !important;
}

.stat-css .stat-value {
    color: #e65100 !important; /* Dark orange for CSS minify */
    font-weight: 700 !important;
}

.stat-css .stat-label {
    color: #bf360c !important;
}

.stat-js .stat-value {
    color: #6a1b9a !important; /* Dark purple for JS minify */
    font-weight: 700 !important;
}

.stat-js .stat-label {
    color: #4a148c !important;
}

/* Database Cleanup Module Statistics Colors */
.stat-revisions .stat-value {
    color: #d32f2f !important; /* Dark red for revisions */
    font-weight: 700 !important;
}

.stat-revisions .stat-label {
    color: #b71c1c !important;
}

.stat-drafts .stat-value {
    color: #f57c00 !important; /* Dark amber for drafts */
    font-weight: 700 !important;
}

.stat-drafts .stat-label {
    color: #e65100 !important;
}

.stat-spam .stat-value {
    color: #7b1fa2 !important; /* Dark purple for spam */
    font-weight: 700 !important;
}

.stat-spam .stat-label {
    color: #4a148c !important;
}

.stat-transients .stat-value {
    color: #1976d2 !important; /* Dark blue for transients */
    font-weight: 700 !important;
}

.stat-transients .stat-label {
    color: #0d47a1 !important;
}

/* Critical Resource Optimizer Module Statistics Colors */
.stat-critical .stat-value {
    color: #2e7d32 !important; /* Dark green for critical CSS files */
    font-weight: 700 !important;
}

.stat-critical .stat-label {
    color: #1b5e20 !important;
}

.stat-pages .stat-value {
    color: #1565c0 !important; /* Dark blue for optimized pages */
    font-weight: 700 !important;
}

.stat-pages .stat-label {
    color: #0d47a1 !important;
}

.stat-resources .stat-value {
    color: #e65100 !important; /* Dark orange for resources */
    font-weight: 700 !important;
}

.stat-resources .stat-label {
    color: #bf360c !important;
}

.stat-performance .stat-value {
    color: #6a1b9a !important; /* Dark purple for performance */
    font-weight: 700 !important;
}

.stat-performance .stat-label {
    color: #4a148c !important;
}

/* Heartbeat Control Module Statistics Colors */
.stat-admin .stat-value {
    color: #2e7d32 !important; /* Dark green for admin status */
    font-weight: 700 !important;
}

.stat-admin .stat-label {
    color: #1b5e20 !important;
}

.stat-editor .stat-value {
    color: #1565c0 !important; /* Dark blue for editor status */
    font-weight: 700 !important;
}

.stat-editor .stat-label {
    color: #0d47a1 !important;
}

.stat-frontend .stat-value {
    color: #e65100 !important; /* Dark orange for frontend status */
    font-weight: 700 !important;
}

.stat-frontend .stat-label {
    color: #bf360c !important;
}

/* Status-specific colors for heartbeat control */
.stat-value.status-default {
    color: #1976d2 !important; /* Blue for default */
}

.stat-value.status-modify {
    color: #f57c00 !important; /* Orange for modified */
}

.stat-value.status-disable {
    color: #d32f2f !important; /* Red for disabled */
}

/* WordPress Core Tweaks Module Statistics Colors */
.stat-css-versions .stat-value {
    color: #2e7d32 !important; /* Dark green for CSS versions */
    font-weight: 700 !important;
}

.stat-css-versions .stat-label {
    color: #1b5e20 !important;
}

.stat-js-versions .stat-value {
    color: #1565c0 !important; /* Dark blue for JS versions */
    font-weight: 700 !important;
}

.stat-js-versions .stat-label {
    color: #0d47a1 !important;
}

.stat-excluded .stat-value {
    color: #e65100 !important; /* Dark orange for excluded handles */
    font-weight: 700 !important;
}

.stat-excluded .stat-label {
    color: #bf360c !important;
}

.stat-autosave .stat-value {
    color: #6a1b9a !important; /* Dark purple for autosave interval */
    font-weight: 700 !important;
}

.stat-autosave .stat-label {
    color: #4a148c !important;
}

/* Enhanced Database Cleanup Options Styling */

/* Card Header Actions */
.card-header-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

.card-header-actions .button-small {
    padding: 4px 12px;
    font-size: 12px;
    height: auto;
    line-height: 1.4;
}

/* Cleanup Intro Section */
.cleanup-intro {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #4caf50;
}

.cleanup-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 13px;
}

.total-items {
    color: #2e7d32;
    font-weight: 600;
}

.estimated-space {
    color: #666;
    font-style: italic;
}

/* Cleanup Sections */
.cleanup-section {
    margin-bottom: 24px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.section-title {
    margin: 0;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title .dashicons {
    color: #4caf50;
    font-size: 16px;
}

/* Enhanced Cleanup Options */
.cleanup-option {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
    position: relative;
}

.cleanup-option:last-child {
    border-bottom: none;
}

.cleanup-option:hover {
    background: #fafbfc;
}

.cleanup-option.recommended {
    border-left: 3px solid #4caf50;
}

.cleanup-option.advanced {
    border-left: 3px solid #ff9800;
}

.cleanup-option[data-impact="high"] {
    border-left: 3px solid #f44336;
}

/* Checkbox Items with Enhanced Layout */
.cleanup-option .checkbox-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    cursor: pointer;
}

.option-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.option-text {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.option-text strong {
    color: #1e293b;
    font-weight: 600;
}

/* Badges */
.recommended-badge,
.advanced-badge,
.caution-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.recommended-badge {
    background: #e8f5e8;
    color: #2e7d32;
}

.advanced-badge {
    background: #fff3e0;
    color: #f57c00;
}

.caution-badge {
    background: #ffebee;
    color: #d32f2f;
}

/* Count Indicators */
.cleanup-option .count {
    font-weight: 700;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-left: auto;
}

.count.high {
    background: #ffebee;
    color: #d32f2f;
}

.count.medium {
    background: #fff3e0;
    color: #f57c00;
}

.count.low {
    background: #e8f5e8;
    color: #2e7d32;
}

/* Enhanced Descriptions */
.cleanup-option .description {
    margin-top: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.5;
    color: #64748b;
}

/* Impact Information */
.impact-info {
    display: flex;
    gap: 12px;
    margin-top: 8px;
    font-size: 11px;
}

.impact-level,
.safety-level {
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.impact-level.low {
    background: #e8f5e8;
    color: #2e7d32;
}

.impact-level.medium {
    background: #fff3e0;
    color: #f57c00;
}

.impact-level.high {
    background: #ffebee;
    color: #d32f2f;
}

.safety-level.safe {
    background: #e3f2fd;
    color: #1976d2;
}

.safety-level.review {
    background: #fff3e0;
    color: #f57c00;
}

.safety-level.caution {
    background: #ffebee;
    color: #d32f2f;
}

/* Sub-options */
.sub-option {
    margin-top: 12px;
    padding: 12px;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.sub-option label {
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.sub-option input[type="number"] {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
}

.help-text {
    color: #6b7280;
    font-size: 11px;
    font-style: italic;
    margin-left: 8px;
}

/* Cleanup Summary Footer */
.cleanup-summary-footer {
    margin-top: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.selected-count {
    color: #4caf50;
}

.estimated-impact {
    color: #666;
}

.safety-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.safety-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .cleanup-summary {
        flex-direction: column;
        gap: 8px;
    }

    .summary-stats {
        flex-direction: column;
        gap: 4px;
    }

    .card-header-actions {
        flex-direction: column;
        gap: 4px;
        margin-top: 8px;
        margin-left: 0;
    }

    .option-text {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .sub-option {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* Interactive Features for Cleanup Options */

/* Button States */
.card-header-actions .button.button-primary {
    background: #4caf50;
    border-color: #4caf50;
    color: white;
}

.card-header-actions .button.button-secondary {
    background: #f1f5f9;
    border-color: #d1d5db;
    color: #374151;
}

.card-header-actions .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Selection Highlight Animation */
.selection-highlight {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%) !important;
    transform: scale(1.02);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* Option Hover Effects */
.option-hover {
    background: #fafbfc !important;
    border-color: #4caf50 !important;
}

/* Impact Level Colors for Summary */
.estimated-impact.impact-low {
    color: #2e7d32;
}

.estimated-impact.impact-medium {
    color: #f57c00;
}

.estimated-impact.impact-high {
    color: #d32f2f;
    font-weight: 700;
}

/* Enhanced Tooltips */
.cleanup-tooltip {
    position: fixed;
    background: #1e293b;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    max-width: 250px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    opacity: 0.95;
}

.cleanup-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1e293b;
}

/* Checkbox Focus States */
.cleanup-option input[type="checkbox"]:focus {
    outline: 2px solid #4caf50;
    outline-offset: 2px;
}

/* Enhanced Count Animations */
.count {
    transition: all 0.2s ease;
}

.cleanup-option:hover .count {
    transform: scale(1.1);
}

/* Button Loading States */
.card-header-actions .button.loading {
    position: relative;
    color: transparent;
}

.card-header-actions .button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Summary Footer Enhancements */
.cleanup-summary-footer {
    transition: all 0.3s ease;
}

.cleanup-summary-footer.has-selections {
    border-color: #4caf50;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .selection-highlight,
    .option-hover,
    .count,
    .cleanup-summary-footer {
        transition: none;
    }

    .card-header-actions .button:hover {
        transform: none;
    }

    .cleanup-option:hover .count {
        transform: none;
    }
}

/* Enhanced Module Cards - Universal Styling */

/* Settings Introduction Sections */
.settings-intro {
    margin-bottom: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border-left: 4px solid var(--redco-primary);
}

.performance-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.indicator-label {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
}

.impact-level {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.impact-level.high {
    background: #dcfce7;
    color: #166534;
}

.impact-level.medium {
    background: #fef3c7;
    color: #92400e;
}

.impact-level.low {
    background: #dbeafe;
    color: #1e40af;
}

/* Settings Sections */
.settings-section {
    margin-bottom: 24px;
}

.section-title {
    margin: 0 0 16px 0;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title .dashicons {
    color: var(--redco-primary);
    font-size: 16px;
}

/* Enhanced Setting Items */
.setting-item.enhanced {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 16px;
    background: white;
    transition: all 0.2s ease;
}

.setting-item.enhanced:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.setting-label strong {
    color: #1e293b;
    font-size: 15px;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.enhanced-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.enhanced-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.help-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: #6b7280;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    cursor: help;
    transition: all 0.2s ease;
}

.help-icon:hover {
    background: var(--redco-primary);
    transform: scale(1.1);
}

.setting-description {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
}

.setting-impact {
    display: flex;
    gap: 16px;
    margin-top: 8px;
    font-size: 12px;
}

.impact-info,
.freshness-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.impact-label,
.freshness-label {
    font-weight: 600;
    color: #374151;
}

.impact-value,
.freshness-value {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.impact-value {
    background: #dcfce7;
    color: #166534;
}

.freshness-value {
    background: #fef3c7;
    color: #92400e;
}

/* Page Cache Exclusion Controls */
.exclusion-intro {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #4caf50;
}

.exclusion-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 13px;
}

.total-pages {
    color: #2e7d32;
    font-weight: 600;
}

.excluded-count {
    color: #666;
}

.exclusion-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 16px;
}

.filter-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-label {
    font-size: 13px;
    color: #374151;
    cursor: pointer;
}

.pages-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
}

.pages-list {
    padding: 0;
}

.page-exclusion-item {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.page-exclusion-item:last-child {
    border-bottom: none;
}

.page-exclusion-item:hover {
    background: #fafbfc;
}

.page-exclusion-item.excluded {
    background: #fef2f2;
    border-left: 3px solid #ef4444;
}

.page-checkbox-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    gap: 12px;
}

.page-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.page-title {
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #6b7280;
}

.page-type {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    text-transform: uppercase;
    font-weight: 600;
}

.page-id {
    color: #9ca3af;
}

.exclusion-status {
    margin-left: auto;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-indicator.cached {
    background: #dcfce7;
    color: #166534;
}

.status-indicator.excluded {
    background: #fef2f2;
    color: #dc2626;
}

.exclusion-summary-footer {
    margin-top: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.summary-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
}

.pages-shown {
    color: #4caf50;
}

.cache-efficiency {
    color: #666;
}

.exclusion-tips {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.exclusion-tips .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Minification Options */
.minification-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.minification-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.minification-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.minification-option.recommended {
    border-left: 3px solid #4caf50;
}

.minification-option.advanced {
    border-left: 3px solid #ff9800;
}

.option-checkbox-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
}

.option-icon {
    font-size: 20px;
    width: 28px;
    text-align: center;
    flex-shrink: 0;
    margin-top: 2px;
}

.option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.option-title strong {
    color: #1e293b;
    font-size: 15px;
}

.option-description {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
}

.option-stats {
    display: flex;
    gap: 16px;
    margin-top: 8px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.stat-label {
    color: #6b7280;
    font-weight: 600;
}

.stat-value {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.stat-value.high {
    background: #dcfce7;
    color: #166534;
}

.stat-value.medium {
    background: #fef3c7;
    color: #92400e;
}

.minification-summary {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.enabled-count {
    color: #4caf50;
}

.estimated-savings {
    color: #666;
}

.compatibility-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.compatibility-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Small Badges */
.recommended-badge.small,
.advanced-badge.small {
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 6px;
}

/* Lazy Load Module Specific Styles */

/* Threshold Slider Container */
.threshold-slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.threshold-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

.threshold-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--redco-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.threshold-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--redco-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.threshold-input {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.threshold-unit {
    font-size: 13px;
    color: #6b7280;
    font-weight: 600;
}

/* Threshold Recommendations */
.threshold-recommendations {
    display: flex;
    gap: 12px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.threshold-preset {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    min-width: 80px;
}

.threshold-preset:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.threshold-preset.active {
    border-color: var(--redco-primary);
    background: #f0f9ff;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.threshold-preset.recommended {
    border-color: var(--redco-primary);
    background: #f8fdf8;
}

.threshold-preset.preset-selected {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.preset-label {
    font-size: 11px;
    font-weight: 600;
    color: #374151;
    text-align: center;
    margin-bottom: 2px;
}

.preset-value {
    font-size: 13px;
    font-weight: 700;
    color: var(--redco-primary);
}

/* Smoothness Info */
.smoothness-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.smoothness-label {
    font-weight: 600;
    color: #374151;
}

.smoothness-value {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    background: #e0f2fe;
    color: #0277bd;
}

/* Exclusion Options (Lazy Load specific) */
.exclusion-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.exclusion-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.exclusion-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.exclusion-option.recommended {
    border-left: 3px solid #4caf50;
}

/* Advanced Exclusions */
.advanced-exclusions {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.advanced-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.advanced-content {
    color: #6b7280;
    font-size: 13px;
}

.code-examples {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.code-example {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-label {
    font-weight: 600;
    color: #374151;
    min-width: 80px;
    font-size: 12px;
}

.code-snippet {
    background: #1e293b;
    color: #e2e8f0;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    flex: 1;
}

/* Lazy Load Notifications */
.lazy-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.lazy-notification.show {
    transform: translateX(0);
}

.lazy-notification.success {
    background: #10b981;
}

.lazy-notification.info {
    background: #3b82f6;
}

.lazy-notification.warning {
    background: #f59e0b;
}

.lazy-notification.error {
    background: #ef4444;
}

/* Loading States for Lazy Load */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design for Lazy Load */
@media (max-width: 768px) {
    .threshold-recommendations {
        justify-content: center;
    }

    .threshold-preset {
        min-width: 70px;
        padding: 6px 8px;
    }

    .threshold-slider-container {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .code-example {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .code-label {
        min-width: auto;
    }
}

/* Heartbeat Control Module Specific Styles */

/* Heartbeat Sections */
.heartbeat-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.heartbeat-section {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.heartbeat-section:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Heartbeat Control Groups */
.heartbeat-control-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    transition: all 0.3s ease;
}

.heartbeat-control-group.group-hover {
    background: #fafbfc;
    border-radius: 6px;
    padding: 12px;
    margin: -12px;
}

.heartbeat-control-group.settings-changed {
    background: #f0f9ff;
    border-radius: 6px;
    padding: 12px;
    margin: -12px;
    border: 1px solid #0ea5e9;
}

.heartbeat-control-group.settings-updated {
    background: #f0fdf4;
    border-radius: 6px;
    padding: 12px;
    margin: -12px;
    border: 1px solid #22c55e;
}

/* Control Main */
.control-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.control-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.control-label strong {
    color: #1e293b;
    font-size: 15px;
}

.control-description {
    color: #6b7280;
    font-size: 13px;
    font-style: italic;
}

.heartbeat-control-select {
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
}

.heartbeat-control-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Frequency Controls */
.frequency-control {
    display: none;
    padding: 12px 16px;
    background: #f8fafc;
    border-radius: 6px;
    border-left: 3px solid var(--redco-primary);
    margin-left: 20px;
}

.frequency-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 13px;
}

.frequency-select {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    background: white;
}

.frequency-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

/* Control Info */
.control-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.info-label {
    font-weight: 600;
    color: #6b7280;
}

.info-value {
    color: #374151;
}

.info-value.recommended {
    color: var(--redco-primary);
    font-weight: 600;
}

/* Heartbeat Summary */
.heartbeat-summary {
    margin-top: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.performance-savings {
    color: var(--redco-primary);
}

.server-load {
    color: #6b7280;
}

.summary-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f59e0b;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.summary-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Heartbeat Notifications */
.heartbeat-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.heartbeat-notification.show {
    transform: translateX(0);
}

.heartbeat-notification.success {
    background: #10b981;
}

.heartbeat-notification.info {
    background: #3b82f6;
}

.heartbeat-notification.warning {
    background: #f59e0b;
}

.heartbeat-notification.error {
    background: #ef4444;
}

/* Loading States for Heartbeat */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design for Heartbeat */
@media (max-width: 768px) {
    .control-main {
        flex-direction: column;
        gap: 12px;
    }

    .heartbeat-control-select {
        min-width: auto;
        width: 100%;
    }

    .summary-stats {
        flex-direction: column;
        gap: 8px;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .frequency-control {
        margin-left: 0;
    }
}

/* Trending Pages - Compact */
.trending-pages-list {
    margin-bottom: 8px;
}

.trending-page-item {
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f1;
}

.trending-page-item:last-child {
    border-bottom: none;
}

.page-info .page-title {
    font-size: 0.8em;
    font-weight: 500;
    color: #1d2327;
    margin-bottom: 3px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.page-stats {
    display: flex;
    gap: 6px;
    font-size: 0.7em;
    color: #646970;
}

.page-stats span {
    background: #f6f7f7;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.7em;
    color: #1d2327;
    font-weight: 500;
}

/* Optimization Tips - Compact */
.optimization-tips-list {
    margin-bottom: 8px;
}

.optimization-tip {
    padding: 6px 8px;
    margin-bottom: 6px;
    border-radius: 3px;
    border-left: 2px solid;
}

.optimization-tip.tip-warning {
    background: #fff8e1;
    border-left-color: #ff9800;
}

.optimization-tip.tip-info {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.optimization-tip.tip-success {
    background: #e8f5e8;
    border-left-color: #4caf50;
}

.tip-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
    font-size: 0.75em;
}

.tip-header strong {
    color: #1d2327;
    font-weight: 600;
}

.tip-icon {
    font-size: 12px;
    color: #1d2327;
}

.tip-message {
    font-size: 0.7em;
    line-height: 1.3;
    color: #1d2327;
    font-weight: 400;
}

/* Historical Trends - Compact */
.trends-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px;
    background: #f6f7f7;
    border-radius: 3px;
}

.trend-stat {
    text-align: center;
}

.trend-value {
    display: block;
    font-size: 0.9em;
    font-weight: 600;
    color: #1d2327;
    line-height: 1.1;
}

.trend-label {
    font-size: 0.65em;
    color: #646970;
    font-weight: 500;
}

.trend-direction {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 0.7em;
    font-weight: 500;
}

.trend-direction.trend-increasing {
    color: #4caf50;
}

.trend-direction.trend-decreasing {
    color: #f44336;
}

.trend-direction.trend-stable {
    color: #646970;
}

.trends-chart {
    margin-bottom: 8px;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 1px;
    height: 30px;
    padding: 3px;
    background: #f9f9f9;
    border-radius: 3px;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, #4caf50, #81c784);
    border-radius: 1px 1px 0 0;
    min-height: 2px;
    transition: all 0.2s ease;
}

.chart-bar:hover {
    background: linear-gradient(to top, #388e3c, #66bb6a);
    transform: scaleY(1.05);
}

/* Enhanced Responsive Design for New Sections */
@media (max-width: 768px) {
    .trends-summary {
        flex-direction: column;
        gap: 6px;
        padding: 4px;
    }

    .page-stats {
        flex-direction: column;
        gap: 3px;
    }

    .optimization-tip {
        padding: 4px 6px;
        margin-bottom: 4px;
    }

    .tip-header {
        font-size: 0.7em;
        margin-bottom: 1px;
    }

    .tip-message {
        font-size: 0.65em;
    }

    .sidebar-section-header {
        padding: 8px 12px;
    }

    .sidebar-section-header h3 {
        font-size: 13px;
    }

    .sidebar-section-content {
        padding: 8px 12px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .stat-item {
        padding: 6px 4px;
    }

    .stat-value {
        font-size: 14px;
    }

    .stat-label {
        font-size: 9px;
    }

    /* Maintain high-contrast colors on mobile */
    .stat-hits .stat-value {
        color: #2e7d32 !important;
        font-weight: 700 !important;
    }

    .stat-hits .stat-label {
        color: #1b5e20 !important;
    }

    .stat-misses .stat-value {
        color: #e65100 !important;
        font-weight: 700 !important;
    }

    .stat-misses .stat-label {
        color: #bf360c !important;
    }

    .stat-size .stat-value {
        color: #1565c0 !important;
        font-weight: 700 !important;
    }

    .stat-size .stat-label {
        color: #0d47a1 !important;
    }

    .stat-ratio .stat-value {
        color: #6a1b9a !important;
        font-weight: 700 !important;
    }

    .stat-ratio .stat-label {
        color: #4a148c !important;
    }

    /* Maintain all module colors on mobile */
    .stat-images .stat-value { color: #2e7d32 !important; font-weight: 700 !important; }
    .stat-images .stat-label { color: #1b5e20 !important; }
    .stat-bandwidth .stat-value { color: #1565c0 !important; font-weight: 700 !important; }
    .stat-bandwidth .stat-label { color: #0d47a1 !important; }
    .stat-threshold .stat-value { color: #e65100 !important; font-weight: 700 !important; }
    .stat-threshold .stat-label { color: #bf360c !important; }
    .stat-featured .stat-value { color: #6a1b9a !important; font-weight: 700 !important; }
    .stat-featured .stat-label { color: #4a148c !important; }

    .stat-files .stat-value { color: #2e7d32 !important; font-weight: 700 !important; }
    .stat-files .stat-label { color: #1b5e20 !important; }
    .stat-saved .stat-value { color: #1565c0 !important; font-weight: 700 !important; }
    .stat-saved .stat-label { color: #0d47a1 !important; }
    .stat-css .stat-value { color: #e65100 !important; font-weight: 700 !important; }
    .stat-css .stat-label { color: #bf360c !important; }
    .stat-js .stat-value { color: #6a1b9a !important; font-weight: 700 !important; }
    .stat-js .stat-label { color: #4a148c !important; }

    .stat-revisions .stat-value { color: #d32f2f !important; font-weight: 700 !important; }
    .stat-revisions .stat-label { color: #b71c1c !important; }
    .stat-drafts .stat-value { color: #f57c00 !important; font-weight: 700 !important; }
    .stat-drafts .stat-label { color: #e65100 !important; }
    .stat-spam .stat-value { color: #7b1fa2 !important; font-weight: 700 !important; }
    .stat-spam .stat-label { color: #4a148c !important; }
    .stat-transients .stat-value { color: #1976d2 !important; font-weight: 700 !important; }
    .stat-transients .stat-label { color: #0d47a1 !important; }

    .stat-critical .stat-value { color: #2e7d32 !important; font-weight: 700 !important; }
    .stat-critical .stat-label { color: #1b5e20 !important; }
    .stat-pages .stat-value { color: #1565c0 !important; font-weight: 700 !important; }
    .stat-pages .stat-label { color: #0d47a1 !important; }
    .stat-resources .stat-value { color: #e65100 !important; font-weight: 700 !important; }
    .stat-resources .stat-label { color: #bf360c !important; }
    .stat-performance .stat-value { color: #6a1b9a !important; font-weight: 700 !important; }
    .stat-performance .stat-label { color: #4a148c !important; }

    .stat-admin .stat-value { color: #2e7d32 !important; font-weight: 700 !important; }
    .stat-admin .stat-label { color: #1b5e20 !important; }
    .stat-editor .stat-value { color: #1565c0 !important; font-weight: 700 !important; }
    .stat-editor .stat-label { color: #0d47a1 !important; }
    .stat-frontend .stat-value { color: #e65100 !important; font-weight: 700 !important; }
    .stat-frontend .stat-label { color: #bf360c !important; }

    .stat-value.status-default { color: #1976d2 !important; }
    .stat-value.status-modify { color: #f57c00 !important; }
    .stat-value.status-disable { color: #d32f2f !important; }
}