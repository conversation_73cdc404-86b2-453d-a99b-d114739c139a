/**
 * Standardized Module Layout CSS
 * Based on Diagnostic & Auto-Fix module design
 * Redco Optimizer Plugin
 */

/* CSS Variables for consistent theming */
:root {
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-secondary: #64748b;
    --redco-success: #4CAF50;
    --redco-warning: #f59e0b;
    --redco-danger: #ef4444;
    --redco-info: #2563eb;
    --redco-light: #f8fafc;
    --redco-dark: #1e293b;
    --redco-border: #e2e8f0;
    --redco-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --redco-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    /* WordPress Admin Bar Height - Standard WordPress CSS Custom Property */
    --wp-admin--admin-bar--height: 32px;
}

/* Module Tab Container */
.redco-module-tab {
    background: #f9fafb;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* Professional Header Section */
.module-header-section {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    margin: 0 -20px 30px -20px;
    padding: 30px 0;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    min-height: 143px;
}

.header-content {
    max-width: 1200px;
    max-height: 200px;
    margin: 0 auto;
    padding: 30px 40px 0 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
}

.header-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-icon .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: white;
}

.header-text h1 {
    color: white;
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2;
}

.header-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 16px;
    line-height: 1.4;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Module Toggle in Header */
.module-toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.module-toggle-wrapper label {
    color: white;
    font-weight: 500;
    margin: 0;
    cursor: pointer;
}

/* Main Layout */
.redco-module-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.module-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.redco-content-main {
    flex: 1;
    min-width: 0;
}

.redco-content-sidebar {
    width: 260px;
    flex-shrink: 0;
}

/* Card Styling */
.redco-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.redco-card:hover {
    box-shadow: var(--redco-shadow-lg);
}

.card-header {
    background: #fafbfc;
    border-bottom: 1px solid var(--redco-border);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.card-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.card-content {
    padding: 24px;
}

/* Sidebar Sections */
.redco-sidebar-section {
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 20px;
    overflow: hidden;
}

.sidebar-section-header {
    background: #fafbfc;
    border-bottom: 1px solid var(--redco-border);
    padding: 20px 24px;
}

.sidebar-section-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-section-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.sidebar-section-content {
    padding: 20px;
}

/* Statistics Display */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-primary);
    margin-bottom: 4px;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: var(--redco-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Enhanced Form Elements */
.form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.form-table th {
    width: 220px;
    padding: 20px 15px 20px 0;
    text-align: left;
    font-weight: 600;
    color: var(--redco-dark);
    vertical-align: top;
    font-size: 14px;
    line-height: 1.4;
}

.form-table td {
    padding: 20px 0;
    vertical-align: top;
    line-height: 1.5;
}

.form-table tr {
    border-bottom: 1px solid #f0f0f1;
    transition: background-color 0.2s ease;
}

.form-table tr:last-child {
    border-bottom: none;
}

.form-table tr:hover {
    background-color: #f8f9fa;
}

/* Enhanced Checkbox Styling */
.form-table td label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    font-weight: 500;
    color: var(--redco-dark);
    cursor: pointer;
    line-height: 1.4;
    transition: color 0.2s ease;
}

.form-table td label:hover {
    color: var(--redco-primary);
}

.form-table td input[type="checkbox"] {
    margin: 2px 0 0 0;
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    accent-color: var(--redco-primary);
}

.form-table td .description {
    margin-top: 8px;
    color: var(--redco-secondary);
    font-size: 13px;
    line-height: 1.5;
    font-style: normal;
}

/* Settings Sections */
.redco-settings-section {
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 24px;
    overflow: hidden;
}

.redco-settings-section h3 {
    background: #fafbfc;
    border-bottom: 1px solid var(--redco-border);
    padding: 20px 24px;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.redco-settings-section h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.redco-settings-section .form-table {
    margin: 0;
}

.redco-settings-section .form-table th,
.redco-settings-section .form-table td {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.redco-settings-section .form-table tr:last-child th,
.redco-settings-section .form-table tr:last-child td {
    border-bottom: none;
}

/* Button Styling */
.button-primary {
    background: var(--redco-primary) !important;
    border-color: var(--redco-primary) !important;
    color: white !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.button-primary:hover {
    background: var(--redco-primary-dark) !important;
    border-color: var(--redco-primary-dark) !important;
    transform: translateY(-1px) !important;
}

.button-secondary {
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.button-secondary:hover {
    transform: translateY(-1px) !important;
}

/* Module Disabled State */
.redco-module-disabled {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin: 20px auto;
    max-width: 600px;
}

.disabled-message .dashicons {
    font-size: 48px;
    color: var(--redco-secondary);
    margin-bottom: 20px;
}

.disabled-message h3 {
    font-size: 24px;
    color: var(--redco-dark);
    margin-bottom: 16px;
}

.disabled-message p {
    color: var(--redco-secondary);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .module-layout {
        flex-direction: column;
    }

    .redco-content-sidebar {
        width: 100%;
        margin-top: 0;
    }

    .header-content {
        padding: 30px 20px 0 20px;
        max-height: 200px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        padding: 30px 10px 0 10px;
        max-height: 200px;
    }

    .header-main {
        flex-direction: column;
        text-align: center;
    }

    .header-text h1 {
        font-size: 24px;
    }

    .redco-module-content {
        padding: 0 10px;
    }

    .module-header-section {
        margin: 0 -10px 20px -10px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .card-content {
        padding: 16px;
    }

    .sidebar-section-header {
        padding: 16px 20px;
    }

    .sidebar-section-content {
        padding: 16px;
    }

    .card-header {
        padding: 16px 20px;
    }

    .redco-settings-section h3 {
        padding: 16px 20px;
    }

    .redco-settings-section .form-table th,
    .redco-settings-section .form-table td {
        padding: 16px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Enhanced File Exclusion Lists */
.checkbox-list {
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    max-height: 250px;
    overflow-y: auto;
}

.checkbox-list .checkbox-item {
    margin-bottom: 12px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.checkbox-list .checkbox-item:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.checkbox-list .checkbox-item:last-child {
    margin-bottom: 0;
}

.checkbox-list .checkbox-item label {
    margin: 0;
    font-weight: 500;
    color: var(--redco-dark);
    cursor: pointer;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.checkbox-list .checkbox-item input[type="checkbox"] {
    margin: 2px 0 0 0;
    flex-shrink: 0;
    accent-color: var(--redco-primary);
}

.checkbox-list .checkbox-item small {
    color: var(--redco-secondary);
    font-size: 12px;
    margin-top: 2px;
    display: block;
}

/* Enhanced Statistics Display */
.stat-item {
    text-align: center;
    padding: 20px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--redco-primary);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-dark));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--redco-primary);
    margin-bottom: 6px;
    display: block;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 11px;
    color: var(--redco-secondary);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-weight: 600;
    line-height: 1.2;
}

/* Enhanced Button Styling */
.redco-sidebar-section .button {
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-decoration: none !important;
}

.redco-sidebar-section .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.redco-sidebar-section .button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Card Headers */
.card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f4f6f8 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 20px 24px;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--redco-primary), transparent);
    opacity: 0.3;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 20px;
    width: 20px;
    height: 20px;
}
